const { v4: uuidv4 } = require('uuid');
const { db } = require('../db/database');
const { datetimeLocalToUTCFixed, getCurrentUTC } = require('../utils/timezone');
class Stream {
  static async create(streamData) {
    try {
      const id = uuidv4();
      let {
        title,
        video_id,
        rtmp_url,
        stream_key,
        platform,
        platform_icon,
        bitrate = 2500,
        resolution,
        fps = 30,
        orientation = 'horizontal',
        loop_video = true,
        schedule_time = null,
        duration = null,
        use_advanced_settings = false,
        user_id,
        schedule_timezone = 'Asia/Jakarta'
      } = streamData;

      const status = schedule_time ? 'scheduled' : 'offline';
      const status_updated_at = await getCurrentUTC();

      const newStream = {
        id,
        title,
        video_id,
        rtmp_url,
        stream_key,
        platform,
        platform_icon,
        bitrate,
        resolution,
        fps,
        orientation,
        loop_video,
        schedule_time,
        schedule_timezone,
        duration,
        status,
        status_updated_at,
        use_advanced_settings,
        user_id
      };

      const createdStream = await db.createStream(newStream);
      console.log('Stream created successfully with ID:', id);
      return createdStream;
    } catch (error) {
      console.error('Error creating stream:', error.message);
      throw error;
    }
  }

  static async findById(id) {
    try {
      const stream = await db.getStreamById(id);
      if (stream) {
        // Convert boolean fields from Supabase
        stream.loop_video = Boolean(stream.loop_video);
        stream.use_advanced_settings = Boolean(stream.use_advanced_settings);
      }
      return stream;
    } catch (error) {
      console.error('Error finding stream:', error.message);
      throw error;
    }
  }

  // Batch load streams by IDs to prevent N+1 query problem
  static async findByIds(ids) {
    if (!ids || ids.length === 0) {
      return [];
    }

    try {
      const { data, error } = await db.supabase
        .from('streams')
        .select('*')
        .in('id', ids);

      if (error) {
        throw error;
      }

      if (data) {
        data.forEach(row => {
          row.loop_video = Boolean(row.loop_video);
          row.use_advanced_settings = Boolean(row.use_advanced_settings);
        });
      }

      return data || [];
    } catch (error) {
      console.error('Error in Stream.findByIds:', error.message);
      throw error;
    }
  }

  static async findActiveByUserId(userId) {
    try {
      const { data, error } = await db.supabase
        .from('streams')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'live');

      if (error) {
        throw error;
      }

      if (data) {
        data.forEach(row => {
          row.loop_video = Boolean(row.loop_video);
          row.use_advanced_settings = Boolean(row.use_advanced_settings);
        });
      }

      return data || [];
    } catch (error) {
      console.error('Error finding active streams by user ID:', error.message);
      throw error;
    }
  }
  static async findAll(userId = null, filter = null, limit = null, offset = null, search = '') {
    try {
      // First get streams without relationship to avoid schema cache issues
      let query = db.supabase
        .from('streams')
        .select('*');

      // Apply filters
      if (userId) {
        query = query.eq('user_id', userId);
      }

      if (filter) {
        query = query.eq('status', filter);
      }

      if (search && search.trim() !== '') {
        const searchTerm = `%${search.trim()}%`;
        query = query.or(`title.ilike.${searchTerm},platform.ilike.${searchTerm},status.ilike.${searchTerm}`);
      }

      // Apply ordering
      query = query.order('created_at', { ascending: false });

      // Apply pagination
      if (limit !== null && offset !== null) {
        query = query.range(offset, offset + limit - 1);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      const streams = data || [];

      // Get video data for each stream that has video_id
      const streamsWithVideos = await Promise.all(streams.map(async (stream) => {
        let videoData = null;

        if (stream.video_id) {
          try {
            const { data: video, error: videoError } = await db.supabase
              .from('videos')
              .select('title, filepath, thumbnail_path, duration, resolution, bitrate, fps')
              .eq('id', stream.video_id)
              .single();

            if (!videoError && video) {
              videoData = video;
            }
          } catch (videoErr) {
            // Silently handle video fetch errors
            console.warn(`[DB] Could not fetch video data for stream ${stream.id}:`, videoErr.message);
          }
        }

        return {
          ...stream,
          loop_video: Boolean(stream.loop_video),
          use_advanced_settings: Boolean(stream.use_advanced_settings),
          // Add video fields for backward compatibility
          video_title: videoData?.title || null,
          video_filepath: videoData?.filepath || null,
          video_thumbnail: videoData?.thumbnail_path || null,
          video_duration: videoData?.duration || null,
          video_resolution: videoData?.resolution || null,
          video_bitrate: videoData?.bitrate || null,
          video_fps: videoData?.fps || null
        };
      }));

      return streamsWithVideos;
    } catch (error) {
      console.error('Error finding streams:', error.message);
      throw error;
    }
  }

  // Count all streams for pagination
  static async countAll(userId = null, filter = null, search = '') {
    try {
      let query = db.supabase
        .from('streams')
        .select('*', { count: 'exact', head: true });

      // Apply filters
      if (userId) {
        query = query.eq('user_id', userId);
      }

      if (filter) {
        query = query.eq('status', filter);
      }

      if (search && search.trim() !== '') {
        const searchTerm = `%${search.trim()}%`;
        query = query.or(`title.ilike.${searchTerm},platform.ilike.${searchTerm},status.ilike.${searchTerm}`);
      }

      const { count, error } = await query;

      if (error) {
        throw error;
      }

      return count || 0;
    } catch (error) {
      console.error('Error counting streams:', error.message);
      throw error;
    }
  }
  static async update(id, streamData) {
    try {
      // Prepare update data
      const updateData = { ...streamData };

      // Add updated timestamp
      updateData.updated_at = new Date().toISOString();

      // Remove schedule_timezone if it exists (handled separately)
      delete updateData.schedule_timezone;

      const updatedStream = await db.updateStream(id, updateData);
      return { id, ...updatedStream };
    } catch (error) {
      console.error('Error updating stream:', error.message);
      throw error;
    }
  }
  static async delete(id, userId) {
    try {
      const { error } = await db.supabase
        .from('streams')
        .delete()
        .eq('id', id)
        .eq('user_id', userId);

      if (error) {
        throw error;
      }

      // Invalidate cache after successful deletion
      try {
        const cacheService = require('../services/cacheService');
        cacheService.invalidateStream(id, userId);
        cacheService.invalidateUser(userId);
      } catch (cacheError) {
        console.error('Error invalidating cache after stream deletion:', cacheError);
        // Don't fail the deletion due to cache errors
      }

      return { success: true, deleted: true };
    } catch (error) {
      console.error('Error deleting stream:', error.message);
      throw error;
    }
  }

  // Delete all streams for a user (used when downgrading to Preview plan)
  static async deleteAllUserStreams(userId) {
    try {
      // First get all streams to log them
      const { data: streams, error: selectError } = await db.supabase
        .from('streams')
        .select('id, title')
        .eq('user_id', userId);

      if (selectError) {
        throw selectError;
      }

      if (!streams || streams.length === 0) {
        console.log(`📭 No streams found for user ${userId}`);
        return { success: true, deleted: 0, streams: [] };
      }

      console.log(`🗑️ Deleting ${streams.length} streams for user ${userId}:`);
      streams.forEach(stream => {
        console.log(`   - ${stream.title} (${stream.id})`);
      });

      // Delete all streams for the user
      const { error: deleteError } = await db.supabase
        .from('streams')
        .delete()
        .eq('user_id', userId);

      if (deleteError) {
        throw deleteError;
      }

      console.log(`✅ Successfully deleted ${streams.length} streams for user ${userId}`);
      return {
        success: true,
        deleted: streams.length,
        streams: streams.map(s => ({ id: s.id, title: s.title }))
      };
    } catch (error) {
      console.error('Error deleting all user streams:', error.message);
      throw error;
    }
  }
  static async updateStatus(id, status, userId, errorMessage = null) {
    try {
      console.log(`[DB] Updating status for stream ${id} to: ${status}`);
      const status_updated_at = await getCurrentUTC();
      let start_time = null;
      let end_time = null;

      // CRITICAL FIX: Only set start_time when going live, and only if not already set
      if (status === 'live') {
        // Check if start_time is already set to prevent overwriting
        const { data: existingStream, error: selectError } = await db.supabase
          .from('streams')
          .select('start_time')
          .eq('id', id)
          .single();

        if (selectError) {
          throw selectError;
        }

        // Only set start_time if it's not already set or is null
        if (!existingStream || !existingStream.start_time) {
          start_time = await getCurrentUTC();
          console.log(`[DB] Setting start_time for stream ${id} to: ${start_time}`);
        } else {
          console.log(`[DB] start_time already set for stream ${id}: ${existingStream.start_time}`);
        }
      } else if (status === 'offline' || status === 'error') {
        end_time = await getCurrentUTC();
      }

      // Prepare update data
      const updateData = {
        status,
        status_updated_at,
        updated_at: new Date().toISOString()
      };

      // Only update start_time if we have a new value
      if (start_time) {
        updateData.start_time = start_time;
      }

      // Only update end_time if we have a new value
      if (end_time) {
        updateData.end_time = end_time;
      }

      // Update stream status in database
      const { error } = await db.supabase
        .from('streams')
        .update(updateData)
        .eq('id', id)
        .eq('user_id', userId);

      if (error) {
        throw error;
      }

      console.log(`[DB] Successfully updated status for stream ${id} to: ${status}`);

      return {
        id,
        status,
        status_updated_at,
        start_time,
        end_time,
        errorMessage,
        updated: true
      };
    } catch (error) {
      console.error('Error updating stream status:', error.message);
      throw error;
    }
  }
  static async getStreamWithVideo(id) {
    try {
      const { data, error } = await db.supabase
        .from('streams')
        .select(`
          *,
          videos (
            title,
            filepath,
            thumbnail_path,
            duration
          )
        `)
        .eq('id', id)
        .single();

      if (error) {
        throw error;
      }

      if (data) {
        data.loop_video = Boolean(data.loop_video);
        data.use_advanced_settings = Boolean(data.use_advanced_settings);

        // Flatten video data for backward compatibility
        if (data.videos) {
          data.video_title = data.videos.title;
          data.video_filepath = data.videos.filepath;
          data.video_thumbnail = data.videos.thumbnail_path;
          data.video_duration = data.videos.duration;
        }
      }

      return data;
    } catch (error) {
      console.error('Error fetching stream with video:', error.message);
      throw error;
    }
  }
  static async isStreamKeyInUse(streamKey, userId, excludeId = null, options = {}) {
    try {
      // Options for different validation modes
      const {
        globalCheck = false,        // Check across all users
        includeInactive = false,    // Include offline/error streams
        debugMode = false          // Enable detailed logging
      } = options;

      let query = db.supabase
        .from('streams')
        .select('*', { count: 'exact', head: true })
        .eq('stream_key', streamKey);

      // Add user filter unless global check is requested
      if (!globalCheck && userId) {
        query = query.eq('user_id', userId);
      }

      // Filter by active statuses only (exclude old/inactive streams)
      if (!includeInactive) {
        query = query.in('status', ['offline', 'live', 'scheduled', 'starting']);
      }

      // Exclude specific stream ID if provided (for updates)
      if (excludeId) {
        query = query.neq('id', excludeId);
      }

      if (debugMode) {
        console.log(`[Stream.isStreamKeyInUse] Checking stream key with options:`, {
          globalCheck,
          includeInactive,
          userId: userId || 'all',
          excludeId: excludeId || 'none'
        });
      }

      const { count, error } = await query;

      if (error) {
        throw new Error(`Failed to check stream key: ${error.message}`);
      }

      const isInUse = count > 0;
      if (debugMode) {
        console.log(`[Stream.isStreamKeyInUse] Result: ${isInUse} (count: ${count})`);
      }

      return isInUse;
    } catch (error) {
      console.error('Error checking stream key:', error.message);
      throw error;
    }
  }

  // Debug method to get detailed information about streams with the same key
  static async getStreamsWithKey(streamKey, userId = null, options = {}) {
    try {
      const {
        globalCheck = false,
        includeInactive = false
      } = options;

      let query = db.supabase
        .from('streams')
        .select('id, title, status, user_id, created_at, updated_at')
        .eq('stream_key', streamKey);

      if (!globalCheck && userId) {
        query = query.eq('user_id', userId);
      }

      if (!includeInactive) {
        query = query.in('status', ['offline', 'live', 'scheduled', 'starting']);
      }

      query = query.order('created_at', { ascending: false });

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to get streams with key: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('Error getting streams with key:', error.message);
      throw error;
    }
  }

  // Cleanup old inactive streams to prevent false positives in stream key validation
  static async cleanupOldStreams(options = {}) {
    try {
      const {
        olderThanDays = 30,     // Remove streams older than X days
        statusesToClean = ['error', 'offline'], // Only clean these statuses
        dryRun = false          // If true, only count what would be deleted
      } = options;

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
      const cutoffISO = cutoffDate.toISOString();

      // Count streams that would be deleted
      const { count, error: countError } = await db.supabase
        .from('streams')
        .select('*', { count: 'exact', head: true })
        .lt('updated_at', cutoffISO)
        .in('status', statusesToClean);

      if (countError) {
        throw new Error(`Failed to count old streams: ${countError.message}`);
      }

      if (dryRun) {
        return { wouldDelete: count, deleted: 0, dryRun: true };
      }

      if (count === 0) {
        return { wouldDelete: 0, deleted: 0, dryRun: false };
      }

      // Delete the streams
      const { error: deleteError } = await db.supabase
        .from('streams')
        .delete()
        .lt('updated_at', cutoffISO)
        .in('status', statusesToClean);

      if (deleteError) {
        throw new Error(`Failed to delete old streams: ${deleteError.message}`);
      }

      return { wouldDelete: count, deleted: count, dryRun: false };
    } catch (error) {
      console.error('Error in cleanupOldStreams:', error.message);
      throw error;
    }
  }

  static async findScheduledInRange(endTime) {
    try {
      const endTimeISO = endTime.toISOString();
      console.log(`[DB] Searching for streams scheduled before ${endTimeISO} with status 'scheduled'`);

      // First get the scheduled streams without relationship
      const { data: streams, error: streamsError } = await db.supabase
        .from('streams')
        .select('*')
        .eq('status', 'scheduled')
        .neq('status', 'starting')
        .not('schedule_time', 'is', null)
        .lte('schedule_time', endTimeISO)
        .order('schedule_time', { ascending: true });

      if (streamsError) {
        throw new Error(`Failed to find scheduled streams: ${streamsError.message}`);
      }

      const scheduledStreams = streams || [];
      console.log(`[DB] Found ${scheduledStreams.length} scheduled streams in database`);

      // Get video data for streams that have video_id using individual queries
      // This approach avoids the relationship cache issue
      const transformedStreams = await Promise.all(scheduledStreams.map(async (stream) => {
        let videoData = null;

        if (stream.video_id) {
          try {
            const { data: video, error: videoError } = await db.supabase
              .from('videos')
              .select('title, filepath, thumbnail_path, duration, resolution, bitrate, fps')
              .eq('id', stream.video_id)
              .single();

            if (!videoError && video) {
              videoData = video;
            }
          } catch (videoErr) {
            console.warn(`[DB] Could not fetch video data for stream ${stream.id}:`, videoErr.message);
          }
        }

        const result = {
          ...stream,
          loop_video: Boolean(stream.loop_video),
          use_advanced_settings: Boolean(stream.use_advanced_settings),
          // Add video fields with prefixed names for compatibility
          video_title: videoData?.title || null,
          video_filepath: videoData?.filepath || null,
          video_thumbnail: videoData?.thumbnail_path || null,
          video_duration: videoData?.duration || null,
          video_resolution: videoData?.resolution || null,
          video_bitrate: videoData?.bitrate || null,
          video_fps: videoData?.fps || null
        };

        console.log(`[DB] Stream ${result.id}: ${result.title} scheduled for ${result.schedule_time}`);
        return result;
      }));

      return transformedStreams;
    } catch (error) {
      console.error('Error finding scheduled streams:', error.message);
      throw error;
    }
  }
  static async findAllWithIncorrectTimezone() {
    try {
      const { data, error } = await db.supabase
        .from('streams')
        .select('*')
        .not('schedule_time', 'is', null)
        .eq('status', 'scheduled')
        .not('schedule_time', 'like', '%Z');

      if (error) {
        throw new Error(`Failed to find streams with incorrect timezone: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('Error finding streams with incorrect timezone:', error.message);
      throw error;
    }
  }
}
module.exports = Stream;
