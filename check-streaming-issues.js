#!/usr/bin/env node

/**
 * Check for Streaming Issues
 * Identifies users who should be able to stream but are being incorrectly blocked
 */

const { supabase } = require('./db/database');
const Subscription = require('./models/Subscription');

class StreamingIssueChecker {
  constructor() {
    this.issues = [];
  }

  async run() {
    try {
      console.log('🔍 StreamOnPod: Streaming Issue Checker');
      console.log('='.repeat(50));
      console.log(`Time: ${new Date().toISOString()}\n`);

      // Check all users with active subscriptions
      await this.checkAllActiveSubscriptions();
      
      // Check for specific PodLite issues
      await this.checkPodLiteSpecifically();
      
      // Summary
      await this.printSummary();

    } catch (error) {
      console.error('❌ Check failed:', error.message);
      process.exit(1);
    }
  }

  async checkAllActiveSubscriptions() {
    console.log('📋 Checking All Active Subscriptions');
    console.log('-'.repeat(40));

    try {
      // Get users with active subscriptions (excluding Preview)
      const { data: users, error } = await supabase
        .from('users')
        .select(`
          id,
          username,
          user_subscriptions!inner (
            status,
            end_date,
            subscription_plans!inner (
              name,
              max_streaming_slots
            )
          )
        `)
        .eq('user_subscriptions.status', 'active')
        .neq('user_subscriptions.subscription_plans.name', 'Preview')
        .order('username');

      if (error) {
        throw new Error(`Failed to fetch users with subscriptions: ${error.message}`);
      }

      // Get stream counts for each user
      const usersWithStats = await Promise.all((users || []).map(async (user) => {
        // Get total streams count
        const { count: totalStreams, error: totalError } = await supabase
          .from('streams')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.id);

        // Get live streams count
        const { count: liveStreams, error: liveError } = await supabase
          .from('streams')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.id)
          .eq('status', 'live');

        if (totalError || liveError) {
          console.warn(`Warning: Could not get stream counts for user ${user.username}`);
        }

        const subscription = user.user_subscriptions[0];
        const plan = subscription.subscription_plans;

        return {
          id: user.id,
          username: user.username,
          plan_name: plan.name,
          max_streaming_slots: plan.max_streaming_slots,
          status: subscription.status,
          end_date: subscription.end_date,
          total_streams: totalStreams || 0,
          live_streams: liveStreams || 0
        };
      }));

      console.log(`Found ${usersWithStats.length} users with active paid subscriptions:\n`);

      for (const user of usersWithStats) {
      console.log(`👤 ${user.username} (${user.plan_name})`);
      console.log(`   Max slots: ${user.max_streaming_slots}`);
      console.log(`   Total streams: ${user.total_streams}`);
      console.log(`   Live streams: ${user.live_streams}`);
      
      // Check if user should be able to create streams
      const shouldBeAbleToStream = user.total_streams < user.max_streaming_slots;
      
      try {
        const quotaCheck = await Subscription.checkStreamingSlotLimit(user.id);
        const isBlocked = quotaCheck.hasLimit;
        
        console.log(`   Quota check: ${isBlocked ? '🚫 BLOCKED' : '✅ ALLOWED'} (${quotaCheck.currentSlots}/${quotaCheck.maxSlots})`);
        
        // Identify potential issues
        if (shouldBeAbleToStream && isBlocked) {
          console.log(`   ⚠️  POTENTIAL ISSUE: User should be able to stream but is blocked!`);
          this.issues.push({
            type: 'incorrectly_blocked',
            user: user.username,
            userId: user.id,
            plan: user.plan_name,
            maxSlots: user.max_streaming_slots,
            currentStreams: user.total_streams,
            quotaResult: quotaCheck
          });
        } else if (!shouldBeAbleToStream && !isBlocked) {
          console.log(`   ⚠️  POTENTIAL ISSUE: User should be blocked but is allowed!`);
          this.issues.push({
            type: 'incorrectly_allowed',
            user: user.username,
            userId: user.id,
            plan: user.plan_name,
            maxSlots: user.max_streaming_slots,
            currentStreams: user.total_streams,
            quotaResult: quotaCheck
          });
        } else {
          console.log(`   ✅ Working correctly`);
        }
        
      } catch (error) {
        console.log(`   ❌ Error checking quota: ${error.message}`);
        this.issues.push({
          type: 'quota_check_error',
          user: user.username,
          userId: user.id,
          error: error.message
        });
      }

        console.log('');
      }
    } catch (error) {
      console.error('❌ Error checking active subscriptions:', error.message);
      throw error;
    }
  }

  async checkPodLiteSpecifically() {
    console.log('🎯 Specific PodLite Analysis');
    console.log('-'.repeat(40));

    try {
      // Get PodLite users
      const { data: users, error } = await supabase
        .from('users')
        .select(`
          id,
          username,
          user_subscriptions!inner (
            id,
            status,
            created_at,
            end_date,
            subscription_plans!inner (
              name,
              max_streaming_slots
            )
          )
        `)
        .eq('user_subscriptions.subscription_plans.name', 'PodLite')
        .order('user_subscriptions.created_at', { ascending: false });

      if (error) {
        throw new Error(`Failed to fetch PodLite users: ${error.message}`);
      }

      // Get stream counts for each user
      const podliteUsers = await Promise.all((users || []).map(async (user) => {
        const { count: streamCount, error: countError } = await supabase
          .from('streams')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.id);

        if (countError) {
          console.warn(`Warning: Could not get stream count for user ${user.username}`);
        }

        const subscription = user.user_subscriptions[0];
        const plan = subscription.subscription_plans;

        return {
          id: user.id,
          username: user.username,
          subscription_id: subscription.id,
          status: subscription.status,
          created_at: subscription.created_at,
          end_date: subscription.end_date,
          max_streaming_slots: plan.max_streaming_slots,
          stream_count: streamCount || 0
        };
      }));

      console.log(`Found ${podliteUsers.length} PodLite subscriptions:\n`);

      for (const user of podliteUsers) {
      console.log(`👤 ${user.username}`);
      console.log(`   Subscription: ${user.subscription_id}`);
      console.log(`   Status: ${user.status}`);
      console.log(`   Created: ${new Date(user.created_at).toLocaleDateString()}`);
      console.log(`   Expires: ${user.end_date ? new Date(user.end_date).toLocaleDateString() : 'Never'}`);
      console.log(`   Streams: ${user.stream_count}/${user.max_streaming_slots}`);

      if (user.status === 'active') {
        // Test the specific scenario that was reported
        if (user.stream_count === 0) {
          console.log(`   🧪 Testing: User with 0 streams should be able to create 1 stream`);
          
          try {
            const quotaCheck = await Subscription.checkStreamingSlotLimit(user.id);
            if (quotaCheck.hasLimit) {
              console.log(`   ❌ ISSUE FOUND: User with 0 streams is blocked from streaming!`);
              this.issues.push({
                type: 'podlite_zero_streams_blocked',
                user: user.username,
                userId: user.id,
                quotaResult: quotaCheck
              });
            } else {
              console.log(`   ✅ Correct: User can create streams`);
            }
          } catch (error) {
            console.log(`   ❌ Error testing quota: ${error.message}`);
          }
        }
      }

        console.log('');
      }
    } catch (error) {
      console.error('❌ Error checking PodLite users:', error.message);
      throw error;
    }
  }

  async printSummary() {
    console.log('📊 ISSUE SUMMARY');
    console.log('='.repeat(50));
    
    if (this.issues.length === 0) {
      console.log('✅ No streaming issues found!');
      console.log('All users are correctly able to stream within their plan limits.');
    } else {
      console.log(`⚠️  Found ${this.issues.length} potential issues:\n`);
      
      for (let i = 0; i < this.issues.length; i++) {
        const issue = this.issues[i];
        console.log(`${i + 1}. ${issue.type.toUpperCase()}`);
        console.log(`   User: ${issue.user} (${issue.userId})`);
        
        if (issue.type === 'incorrectly_blocked') {
          console.log(`   Problem: User should be able to stream but is blocked`);
          console.log(`   Plan: ${issue.plan} (${issue.maxSlots} slots)`);
          console.log(`   Current streams: ${issue.currentStreams}`);
          console.log(`   Quota result: ${JSON.stringify(issue.quotaResult, null, 4)}`);
        } else if (issue.type === 'podlite_zero_streams_blocked') {
          console.log(`   Problem: PodLite user with 0 streams cannot create streams`);
          console.log(`   Quota result: ${JSON.stringify(issue.quotaResult, null, 4)}`);
        } else if (issue.error) {
          console.log(`   Error: ${issue.error}`);
        }
        
        console.log('');
      }
      
      console.log('RECOMMENDED ACTIONS:');
      console.log('1. Check for duplicate subscriptions');
      console.log('2. Verify subscription data integrity');
      console.log('3. Test quota logic with affected users');
      console.log('4. Check database for corruption issues');
    }
    
    console.log('='.repeat(50));
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Check interrupted by user');
  process.exit(1);
});

// Run the check
const checker = new StreamingIssueChecker();
checker.run().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
