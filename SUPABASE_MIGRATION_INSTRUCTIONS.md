# StreamOnPod Supabase Migration Instructions

## 🎯 Migration Overview
This guide will help you migrate StreamOnPod from SQLite to Supabase PostgreSQL.

**Estimated Time**: 4-6 hours
**Complexity**: Medium
**Risk Level**: Medium (with proper backups)

## 📋 Prerequisites
- [ ] Node.js and npm installed
- [ ] Supabase account created
- [ ] Git repository backed up
- [ ] Database backup verified: ./db/streamonpod_pre_supabase_1754436384788.db

## 🚀 Phase 1: Supabase Project Setup (30 minutes)

### 1.1 Create Supabase Project
1. Go to https://supabase.com/dashboard
2. Click "New Project"
3. Choose organization and enter project details:
   - Name: StreamOnPod
   - Database Password: [Generate strong password]
   - Region: [Choose closest to your users]
4. Wait for project creation (2-3 minutes)

### 1.2 Get Project Credentials
1. Go to Settings > API
2. Copy the following values:
   - Project URL
   - Project API Key (anon/public)
   - Project API Key (service_role/secret)

### 1.3 Install Supabase Dependencies
```bash
npm install @supabase/supabase-js
npm install --save-dev @supabase/cli
```

## 🏗️ Phase 2: Schema Migration (45 minutes)

### 2.1 Apply Database Schema
1. Go to Supabase Dashboard > SQL Editor
2. Copy and paste the contents of: **./supabase_schema.sql** (CORRECTED VERSION)
3. Click "Run" to execute the schema
4. Verify all tables are created in Table Editor

**✅ NOTE**: The schema file has been corrected to resolve boolean data type issues (INTEGER 0/1 → BOOLEAN FALSE/TRUE).

### 2.2 Configure Row Level Security
The schema includes basic RLS policies. Review and adjust as needed for your security requirements.

## 📤 Phase 3: Data Migration (30 minutes)

### 3.1 Import Data
1. In Supabase Dashboard > SQL Editor
2. Copy and paste the contents of: **./supabase_data_fixed.sql** (CORRECTED VERSION)
3. Click "Run" to import all data
4. Verify data in Table Editor

**✅ NOTE**: The data file has been corrected to properly convert boolean values (SQLite 0/1 → PostgreSQL FALSE/TRUE).

### 3.2 Verify Data Integrity
Check record counts match your SQLite database:
- referrals: 0 records
- role_permissions: 117 records
- referral_earnings: 0 records
- withdrawal_requests: 0 records
- streams: 7 records
- notifications: 37 records
- subscription_plans: 5 records
- users: 93 records
- transactions: 88 records
- videos: 28 records
- stream_history: 0 records
- referral_clicks: 0 records
- user_subscriptions: 113 records

## 🔧 Phase 4: Code Refactoring (2-3 hours)

### 4.1 Environment Configuration
Add to your .env file:
```
SUPABASE_URL=your-project-url
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key
```

### 4.2 Database Connection
Replace SQLite connection with Supabase client.
See: supabase-database-adapter.js (will be generated)

### 4.3 Query Migration
Convert SQLite queries to Supabase syntax.
See: supabase-query-examples.js (will be generated)

## 🧪 Phase 5: Testing (1 hour)

### 5.1 Unit Testing
- [ ] Test database connections
- [ ] Test CRUD operations
- [ ] Test authentication
- [ ] Test subscription management

### 5.2 Integration Testing
- [ ] Test user registration/login
- [ ] Test stream creation/management
- [ ] Test video upload/playback
- [ ] Test payment processing

## 🔄 Phase 6: Rollback Plan

If issues arise:
1. Stop the application
2. Restore SQLite database: ./db/streamonpod_pre_supabase_1754436384788.db
3. Revert code changes from backup
4. Restart application

## 📞 Support Resources

- Migration log: ./supabase_migration_log.txt
- Schema file: ./supabase_schema.sql
- Data file: ./supabase_data.sql
- Supabase Documentation: https://supabase.com/docs

## ✅ Migration Checklist

- [ ] Phase 1: Supabase project created
- [ ] Phase 2: Schema migrated successfully
- [ ] Phase 3: Data imported and verified
- [ ] Phase 4: Code refactored and tested
- [ ] Phase 5: All tests passing
- [ ] Phase 6: Production deployment

---

**Next Steps**: 
1. Follow this guide step by step
2. Run the code generation tools
3. Test thoroughly before production deployment
