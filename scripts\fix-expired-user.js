/**
 * Fix specific user with expired subscription
 * This script handles users who still show incorrect slot limits after expiration
 */

const { supabase } = require('../db/database');
const Subscription = require('../models/Subscription');

async function fixExpiredUser(username) {
  console.log(`🔍 Checking user: ${username}\n`);

  try {
    // Get user details
    const { data: users, error } = await supabase
      .from('users')
      .select('*')
      .eq('username', username)
      .limit(1);

    if (error) {
      throw new Error(`Failed to fetch user: ${error.message}`);
    }

    const user = users && users.length > 0 ? users[0] : null;

    if (!user) {
      console.log('❌ User not found!');
      return;
    }

    console.log('👤 User Details:', {
      id: user.id,
      username: user.username,
      plan_type: user.plan_type,
      max_streaming_slots: user.max_streaming_slots,
      max_storage_gb: user.max_storage_gb
    });

    // Get user's current subscription including expired ones
    const subscription = await Subscription.getUserSubscriptionIncludingExpired(user.id);
    
    console.log('\n📋 Subscription Details:', subscription ? {
      plan_name: subscription.plan_name,
      status: subscription.status,
      end_date: subscription.end_date,
      isExpired: subscription.isExpired,
      max_streaming_slots: subscription.max_streaming_slots,
      max_storage_gb: subscription.max_storage_gb
    } : 'No subscription found');

    // Check if subscription is expired
    if (subscription && subscription.isExpired) {
      console.log('\n⚠️ User has expired subscription! Processing...');
      
      // Handle expired subscription
      const success = await Subscription.handleExpiredSubscription(user.id);
      
      if (success) {
        console.log('✅ Successfully processed expired subscription');
        
        // Verify the fix
        const updatedUser = await new Promise((resolve, reject) => {
          db.get(
            'SELECT * FROM users WHERE id = ?',
            [user.id],
            (err, row) => {
              if (err) reject(err);
              else resolve(row);
            }
          );
        });

        console.log('\n✅ Updated User Details:', {
          plan_type: updatedUser.plan_type,
          max_streaming_slots: updatedUser.max_streaming_slots,
          max_storage_gb: updatedUser.max_storage_gb
        });

        // Check new subscription
        const newSubscription = await Subscription.getUserSubscription(user.id);
        console.log('\n✅ New Active Subscription:', newSubscription ? {
          plan_name: newSubscription.plan_name,
          status: newSubscription.status,
          end_date: newSubscription.end_date
        } : 'No active subscription (Preview plan)');

      } else {
        console.log('❌ Failed to process expired subscription');
      }
    } else if (subscription && !subscription.isExpired) {
      console.log('\n✅ User has active subscription - no action needed');
    } else {
      console.log('\n⚠️ User has no subscription - ensuring Preview plan settings...');
      
      // Make sure user is properly set to Preview plan
      const previewPlan = await Subscription.getPlanByName('Preview');
      if (previewPlan) {
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE users SET plan_type = ?, max_streaming_slots = ?, max_storage_gb = ? WHERE id = ?',
            ['Preview', previewPlan.max_streaming_slots, previewPlan.max_storage_gb, user.id],
            function (err) {
              if (err) reject(err);
              else resolve();
            }
          );
        });

        // Create Preview subscription if doesn't exist
        const activeSubscription = await Subscription.getUserSubscription(user.id);
        if (!activeSubscription) {
          await Subscription.createSubscription({
            user_id: user.id,
            plan_id: previewPlan.id,
            status: 'active',
            end_date: null, // Preview plan has no expiration
            payment_method: 'free'
          });
        }

        console.log('✅ User set to Preview plan with correct limits');
      }
    }

    console.log('\n🎉 User fix completed!');

  } catch (error) {
    console.error('❌ Error fixing user:', error);
  }
}

async function checkAllExpiredUsers() {
  console.log('🔍 Checking all users with expired subscriptions...\n');

  try {
    // Find users with expired subscriptions
    const { data: expiredUsers, error } = await supabase
      .from('users')
      .select(`
        id,
        username,
        plan_type,
        max_streaming_slots,
        max_storage_gb,
        user_subscriptions!left (
          end_date,
          status,
          subscription_plans!inner (
            name
          )
        )
      `)
      .eq('user_subscriptions.status', 'active')
      .neq('user_subscriptions.subscription_plans.name', 'Preview')
      .lt('user_subscriptions.end_date', new Date().toISOString());

    if (error) {
      throw new Error(`Failed to fetch expired users: ${error.message}`);
    }

    // Transform data to match expected format
    const formattedExpiredUsers = (expiredUsers || [])
      .filter(user => user.user_subscriptions && user.user_subscriptions.length > 0)
      .map(user => {
        const subscription = user.user_subscriptions[0];
        return {
          id: user.id,
          username: user.username,
          plan_type: user.plan_type,
          max_streaming_slots: user.max_streaming_slots,
          max_storage_gb: user.max_storage_gb,
          end_date: subscription.end_date,
          status: subscription.status,
          subscription_plan_name: subscription.subscription_plans.name
        };
      });

    console.log(`Found ${formattedExpiredUsers.length} users with expired subscriptions`);

    for (const user of formattedExpiredUsers) {
      console.log(`\n📉 Processing ${user.username}...`);
      await fixExpiredUser(user.username);
    }

    console.log('\n✅ All expired users processed!');

  } catch (error) {
    console.error('❌ Error checking expired users:', error);
  }
}

// Command line interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage:');
    console.log('  node scripts/fix-expired-user.js <username>  - Fix specific user');
    console.log('  node scripts/fix-expired-user.js --all       - Fix all expired users');
    process.exit(1);
  }

  if (args[0] === '--all') {
    checkAllExpiredUsers().then(() => {
      console.log('\n✅ All done!');
      process.exit(0);
    }).catch(error => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
  } else {
    const username = args[0];
    fixExpiredUser(username).then(() => {
      console.log('\n✅ All done!');
      process.exit(0);
    }).catch(error => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
  }
}

module.exports = { fixExpiredUser, checkAllExpiredUsers };
