const { db } = require('../db/database');
const { v4: uuidv4 } = require('uuid');

// Storage update locking mechanism to prevent race conditions
const storageLocks = new Map();

class Subscription {
  // Get all subscription plans
  static getAllPlans() {
    return new Promise((resolve, reject) => {
      db.all('SELECT * FROM subscription_plans WHERE is_active = 1 ORDER BY price ASC', [], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows.map(row => ({
          ...row,
          features: JSON.parse(row.features || '[]')
        })));
      });
    });
  }

  // Get all subscription plans with subscriber counts
  static getAllPlansWithSubscribers() {
    return new Promise((resolve, reject) => {
      db.all(`
        SELECT
          sp.*,
          COUNT(u.id) as subscriber_count
        FROM subscription_plans sp
        LEFT JOIN users u ON u.plan_type = sp.name
        WHERE sp.is_active = 1
        GROUP BY sp.id
        ORDER BY sp.price ASC
      `, [], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows.map(row => ({
          ...row,
          features: JSON.parse(row.features || '[]'),
          subscriber_count: row.subscriber_count || 0
        })));
      });
    });
  }

  // Get plan by ID
  static getPlanById(planId) {
    return new Promise((resolve, reject) => {
      db.get('SELECT * FROM subscription_plans WHERE id = ?', [planId], (err, row) => {
        if (err) {
          return reject(err);
        }
        if (row) {
          row.features = JSON.parse(row.features || '[]');
        }
        resolve(row);
      });
    });
  }

  // Get user's current subscription (excluding Preview plan as it's the default)
  static getUserSubscription(userId) {
    return new Promise((resolve, reject) => {
      db.get(`
        SELECT us.*, sp.name as plan_name, sp.max_streaming_slots, sp.max_storage_gb, sp.features
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = ? AND us.status = 'active' AND sp.name != 'Preview'
        ORDER BY us.created_at DESC
        LIMIT 1
      `, [userId], (err, row) => {
        if (err) {
          return reject(err);
        }
        if (row) {
          row.features = JSON.parse(row.features || '[]');

          // Check if subscription has expired
          if (row.end_date && new Date(row.end_date) < new Date()) {
            console.log(`⚠️ Subscription expired for user ${userId}, plan: ${row.plan_name}`);
            // Mark subscription as expired
            this.updateSubscriptionStatus(row.id, 'expired').catch(console.error);
            resolve(null); // Return null for expired subscription
            return;
          }
        }
        resolve(row);
      });
    });
  }

  // Create new subscription
  static createSubscription(subscriptionData) {
    const id = uuidv4();
    const {
      user_id,
      plan_id,
      status = 'active',
      start_date = new Date().toISOString(),
      end_date,
      payment_method,
      payment_id
    } = subscriptionData;

    return new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO user_subscriptions
         (id, user_id, plan_id, status, start_date, end_date, payment_method, payment_id)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [id, user_id, plan_id, status, start_date, end_date, payment_method, payment_id],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id, ...subscriptionData });
        }
      );
    });
  }

  // Update subscription status
  static updateSubscriptionStatus(subscriptionId, status) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE user_subscriptions SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [status, subscriptionId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: subscriptionId, status });
        }
      );
    });
  }

  // Handle expired subscriptions - downgrade users to Preview plan
  static async handleExpiredSubscription(userId) {
    try {
      const User = require('./User');

      // Get Preview plan details
      const previewPlan = await this.getPlanByName('Preview');
      if (!previewPlan) {
        console.error('❌ Preview plan not found for expired subscription handling');
        return;
      }

      // Delete all user streams since Preview plan has 0 slots
      await this.deleteAllUserStreamsForPreviewPlan(userId);

      // Downgrade user to Preview plan
      await User.updatePlan(
        userId,
        'Preview',
        previewPlan.max_streaming_slots,
        previewPlan.max_storage_gb
      );

      console.log(`📉 User ${userId} downgraded to Preview plan due to expired subscription`);
      return true;
    } catch (error) {
      console.error('❌ Error handling expired subscription:', error);
      return false;
    }
  }

  // Delete all user streams when downgrading to Preview plan (0 slots)
  static async deleteAllUserStreamsForPreviewPlan(userId) {
    try {
      const Stream = require('./Stream');

      // Stop any active streams first
      const streamingService = require('../services/streamingService');
      const activeStreamIds = streamingService.getActiveStreams();

      // Get user's streams
      const userStreams = await new Promise((resolve, reject) => {
        db.all('SELECT id FROM streams WHERE user_id = ?', [userId], (err, rows) => {
          if (err) reject(err);
          else resolve(rows.map(row => row.id));
        });
      });

      // Stop any active streams belonging to this user
      for (const streamId of userStreams) {
        if (activeStreamIds.includes(streamId)) {
          console.log(`🛑 Stopping active stream ${streamId} before deletion`);
          try {
            await streamingService.stopStream(streamId);
          } catch (error) {
            console.error(`❌ Error stopping stream ${streamId}:`, error.message);
          }
        }
      }

      // Delete all streams for the user
      const result = await Stream.deleteAllUserStreams(userId);

      if (result.deleted > 0) {
        console.log(`🗑️ Deleted ${result.deleted} streams for Preview plan downgrade`);
      }

      return result;
    } catch (error) {
      console.error('❌ Error deleting user streams for Preview plan:', error);
      return { success: false, deleted: 0, streams: [] };
    }
  }

  // Get live streams count (for display purposes)
  static async getLiveStreamsCount(userId) {
    try {
      const liveStreams = await new Promise((resolve, reject) => {
        db.get(
          "SELECT COUNT(*) as count FROM streams WHERE user_id = ? AND status = 'live'",
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row.count);
          }
        );
      });
      return liveStreams;
    } catch (error) {
      console.error('Error getting live streams count:', error);
      return 0;
    }
  }

  // Check if user has reached streaming slot limit
  static async checkStreamingSlotLimit(userId) {
    try {
      const subscription = await this.getUserSubscription(userId);

      // Get user plan limits if no subscription
      let maxSlots = 0; // Default to 0 slots (Preview plan default)
      if (subscription) {
        maxSlots = subscription.max_streaming_slots;
      } else {
        // Get user's plan limits
        const user = await new Promise((resolve, reject) => {
          db.get('SELECT max_streaming_slots FROM users WHERE id = ?', [userId], (err, row) => {
            if (err) reject(err);
            else resolve(row);
          });
        });
        maxSlots = user ? user.max_streaming_slots : 0; // Use user's actual limit, default to 0
      }

      // Count current streams (all streams except those that are explicitly deleted)
      // This includes 'offline', 'live', and 'scheduled' streams as they all occupy slots
      const currentStreams = await new Promise((resolve, reject) => {
        db.get(
          "SELECT COUNT(*) as count FROM streams WHERE user_id = ?",
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row.count);
          }
        );
      });

      const hasLimit = maxSlots !== -1 && currentStreams >= maxSlots;

      // console.log(`[Quota Check] User ${userId}: ${currentStreams}/${maxSlots} slots used`); // Disabled for cleaner output

      return {
        hasLimit,
        maxSlots: maxSlots === -1 ? 'Unlimited' : maxSlots,
        currentSlots: currentStreams
      };
    } catch (error) {
      console.error('Error checking streaming slot limit:', error);
      return { hasLimit: true, maxSlots: 0, currentSlots: 0 };
    }
  }

  // Check storage limit
  static async checkStorageLimit(userId, additionalSizeGB = 0) {
    try {
      const subscription = await this.getUserSubscription(userId);

      // Get current storage usage from user table
      const user = await new Promise((resolve, reject) => {
        db.get('SELECT used_storage_gb, max_storage_gb FROM users WHERE id = ?', [userId], (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });

      const currentStorage = user ? user.used_storage_gb || 0 : 0;

      // Use subscription limits if available, otherwise use user plan limits
      const maxStorage = subscription ? subscription.max_storage_gb : (user ? user.max_storage_gb || 5 : 5);
      const wouldExceed = (currentStorage + additionalSizeGB) > maxStorage;

      return {
        hasLimit: wouldExceed,
        maxStorage,
        currentStorage,
        availableStorage: maxStorage - currentStorage
      };
    } catch (error) {
      console.error('Error checking storage limit:', error);
      return { hasLimit: true, maxStorage: 5, currentStorage: 0 };
    }
  }

  // Update user storage usage with race condition protection
  static async updateStorageUsage(userId, sizeGB) {
    // Implement simple locking mechanism
    if (storageLocks.has(userId)) {
      await storageLocks.get(userId);
    }

    const lockPromise = this._updateStorageUsageWithLock(userId, sizeGB);
    storageLocks.set(userId, lockPromise);

    try {
      const result = await lockPromise;
      return result;
    } finally {
      storageLocks.delete(userId);
    }
  }

  // Internal storage update function with database serialization
  static _updateStorageUsageWithLock(userId, sizeGB) {
    return new Promise((resolve, reject) => {
      db.serialize(() => {
        db.get('SELECT used_storage_gb FROM users WHERE id = ?', [userId], (err, row) => {
          if (err) {
            console.error('❌ Error getting current storage:', err);
            return reject(err);
          }

          const currentStorage = row ? (row.used_storage_gb || 0) : 0;
          const newStorage = currentStorage + sizeGB;
          const finalStorage = Math.max(0, newStorage);

          if (newStorage < 0) {
            console.warn(`⚠️ Storage would go negative for user ${userId}: ${currentStorage}GB + ${sizeGB}GB = ${newStorage}GB. Setting to 0GB instead.`);
          }

          db.run(
            'UPDATE users SET used_storage_gb = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [finalStorage, userId],
            function (err) {
              if (err) {
                console.error('❌ Error updating storage:', err);
                return reject(err);
              }
              resolve({
                userId,
                addedStorage: sizeGB,
                previousStorage: currentStorage,
                newStorage: finalStorage,
                wasNegativePrevented: newStorage < 0
              });
            }
          );
        });
      });
    });
  }

  // Create new subscription plan
  static createPlan(planData) {
    const id = uuidv4();
    const {
      name,
      price,
      currency = 'USD',
      billing_period = 'monthly',
      max_streaming_slots = 1,
      max_storage_gb = 5,
      features = []
    } = planData;

    return new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO subscription_plans
         (id, name, price, currency, billing_period, max_streaming_slots, max_storage_gb, features)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [id, name, price, currency, billing_period, max_streaming_slots, max_storage_gb, JSON.stringify(features)],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({
            id,
            name,
            price,
            currency,
            billing_period,
            max_streaming_slots,
            max_storage_gb,
            features
          });
        }
      );
    });
  }

  // Update subscription plan
  static updatePlan(planId, planData) {
    const {
      name,
      price,
      currency,
      billing_period,
      max_streaming_slots,
      max_storage_gb,
      features
    } = planData;

    return new Promise((resolve, reject) => {
      db.run(
        `UPDATE subscription_plans
         SET name = ?, price = ?, currency = ?, billing_period = ?,
             max_streaming_slots = ?, max_storage_gb = ?, features = ?,
             updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [name, price, currency, billing_period, max_streaming_slots, max_storage_gb, JSON.stringify(features), planId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({
            id: planId,
            name,
            price,
            currency,
            billing_period,
            max_streaming_slots,
            max_storage_gb,
            features
          });
        }
      );
    });
  }

  // Delete subscription plan
  static deletePlan(planId) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE subscription_plans SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [planId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: planId, deleted: true });
        }
      );
    });
  }

  // Get all plans including inactive ones (admin only)
  static getAllPlansAdmin() {
    return new Promise((resolve, reject) => {
      db.all('SELECT * FROM subscription_plans ORDER BY price ASC', [], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows.map(row => ({
          ...row,
          features: JSON.parse(row.features || '[]')
        })));
      });
    });
  }

  // Update plan status (activate/deactivate)
  static updatePlanStatus(planId, isActive) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE subscription_plans SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [isActive ? 1 : 0, planId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: planId, is_active: isActive });
        }
      );
    });
  }

  // Get subscribers for a specific plan
  static getPlanSubscribers(planId, planName) {
    return new Promise((resolve, reject) => {
      db.all(
        'SELECT id, username, email, role, is_active, created_at FROM users WHERE plan_type = ? ORDER BY created_at DESC',
        [planName],
        (err, rows) => {
          if (err) {
            return reject(err);
          }
          resolve(rows);
        }
      );
    });
  }

  // Get user's subscription including expired ones (for debugging)
  static getUserSubscriptionAll(userId) {
    return new Promise((resolve, reject) => {
      db.get(`
        SELECT us.*, sp.name as plan_name, sp.max_streaming_slots, sp.max_storage_gb, sp.features
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = ?
        ORDER BY us.created_at DESC
        LIMIT 1
      `, [userId], (err, row) => {
        if (err) {
          return reject(err);
        }
        if (row) {
          row.features = JSON.parse(row.features || '[]');
        }
        resolve(row);
      });
    });
  }

  // Get plan by name (only active plans, case-insensitive)
  static getPlanByName(name) {
    return new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM subscription_plans WHERE LOWER(TRIM(name)) = LOWER(TRIM(?)) AND is_active = 1',
        [name],
        (err, row) => {
          if (err) {
            return reject(err);
          }
          if (row) {
            resolve({
              ...row,
              features: JSON.parse(row.features || '[]')
            });
          } else {
            resolve(null);
          }
        }
      );
    });
  }

  // Get plan by name including inactive plans (for admin operations, case-insensitive)
  static getPlanByNameAll(name) {
    return new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM subscription_plans WHERE LOWER(TRIM(name)) = LOWER(TRIM(?))',
        [name],
        (err, row) => {
          if (err) {
            return reject(err);
          }
          if (row) {
            resolve({
              ...row,
              features: JSON.parse(row.features || '[]')
            });
          } else {
            resolve(null);
          }
        }
      );
    });
  }
}

module.exports = Subscription;
