const multer = require('multer');
const path = require('path');
const fs = require('fs-extra');
const { getUniqueFilename, paths } = require('../utils/storage');

// Regular video storage for non-chunked uploads
const videoStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, paths.videos);
  },
  filename: (req, file, cb) => {
    const uniqueFilename = getUniqueFilename(file.originalname);
    cb(null, uniqueFilename);
  }
});

// Chunked upload storage for temporary chunks
const chunkStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    const chunksDir = path.join(paths.videos, 'chunks');
    fs.ensureDirSync(chunksDir);
    cb(null, chunksDir);
  },
  filename: (req, file, cb) => {
    // Generate temporary filename, will be renamed later
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000000);
    cb(null, `temp_chunk_${timestamp}_${random}.tmp`);
  }
});

const videoFilter = (req, file, cb) => {
  const allowedFormats = ['video/mp4', 'video/quicktime'];
  const fileExt = path.extname(file.originalname).toLowerCase();
  const allowedExts = ['.mp4', '.mov'];
  if (allowedFormats.includes(file.mimetype) || allowedExts.includes(fileExt)) {
    cb(null, true);
  } else {
    cb(new Error('Only .mp4 and .mov formats are allowed for optimal performance'), false);
  }
};

// Chunk filter - less strict since chunks don't have proper MIME types
const chunkFilter = (req, file, cb) => {
  // Allow any file for chunks since they're binary data
  cb(null, true);
};

const uploadVideo = multer({
  storage: videoStorage,
  fileFilter: videoFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 2147483648, // 2GB default
    files: 1
  }
});

// Chunked upload configuration
const CHUNK_SIZE = parseInt(process.env.CHUNK_SIZE) || 10 * 1024 * 1024; // 10MB chunks

const uploadChunk = multer({
  storage: chunkStorage,
  fileFilter: chunkFilter,
  limits: {
    fileSize: CHUNK_SIZE + (1024 * 1024), // 11MB to allow buffer
    files: 1
  }
});

module.exports = {
  uploadVideo,
  uploadChunk,
  CHUNK_SIZE
};