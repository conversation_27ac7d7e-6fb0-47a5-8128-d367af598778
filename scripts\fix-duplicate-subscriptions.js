const { supabase } = require('../db/database');

console.log('🔧 StreamOnPod: Fix Duplicate Active Subscriptions');
console.log('='.repeat(60));

async function fixDuplicateSubscriptions() {
  try {
    console.log('\n📊 Step 1: Identifying users with duplicate active subscriptions...');
    
    // Get all active subscriptions with user and plan info
    const { data: subscriptions, error } = await supabase
      .from('user_subscriptions')
      .select(`
        id,
        user_id,
        created_at,
        users!inner (username),
        subscription_plans!inner (name)
      `)
      .eq('status', 'active')
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch active subscriptions: ${error.message}`);
    }

    // Group by user_id to find duplicates
    const userSubscriptions = {};
    subscriptions.forEach(sub => {
      if (!userSubscriptions[sub.user_id]) {
        userSubscriptions[sub.user_id] = {
          username: sub.users.username,
          subscriptions: [],
          count: 0
        };
      }
      userSubscriptions[sub.user_id].subscriptions.push({
        id: sub.id,
        plan_name: sub.subscription_plans.name,
        created_at: sub.created_at
      });
      userSubscriptions[sub.user_id].count++;
    });

    // Find users with multiple active subscriptions
    const duplicateUsers = Object.entries(userSubscriptions)
      .filter(([userId, data]) => data.count > 1)
      .map(([userId, data]) => ({
        user_id: userId,
        username: data.username,
        active_count: data.count,
        subscription_ids: data.subscriptions.map(s => s.id).join(','),
        plan_names: data.subscriptions.map(s => s.plan_name).join(','),
        created_dates: data.subscriptions.map(s => s.created_at).join(','),
        subscriptions: data.subscriptions
      }))
      .sort((a, b) => b.active_count - a.active_count);
    
    if (duplicateUsers.length === 0) {
      console.log('✅ No duplicate active subscriptions found!');
      process.exit(0);
    }
    
    console.log(`\n⚠️  Found ${duplicateUsers.length} users with duplicate active subscriptions:`);
    console.log('-'.repeat(80));
    
    for (const user of duplicateUsers) {
      console.log(`\n👤 User: ${user.username} (${user.active_count} active subscriptions)`);
      
      const subscriptionIds = user.subscription_ids.split(',');
      const planNames = user.plan_names.split(',');
      const createdDates = user.created_dates.split(',');
      
      console.log('   Subscriptions (newest first):');
      for (let i = 0; i < subscriptionIds.length; i++) {
        console.log(`   ${i + 1}. ID: ${subscriptionIds[i]}, Plan: ${planNames[i]}, Created: ${createdDates[i]}`);
      }
      
      // Keep the newest subscription, mark others as 'superseded'
      const newestSubscriptionId = subscriptionIds[0];
      const oldSubscriptionIds = subscriptionIds.slice(1);
      
      console.log(`   ✅ Keeping newest: ${newestSubscriptionId} (${planNames[0]})`);
      console.log(`   ❌ Marking as superseded: ${oldSubscriptionIds.join(', ')}`);
    }
    
    // Ask for confirmation in production
    if (process.env.NODE_ENV === 'production') {
      console.log('\n⚠️  PRODUCTION MODE DETECTED');
      console.log('This script will modify subscription data.');
      console.log('To proceed, set CONFIRM_DUPLICATE_FIX=true environment variable');
      
      if (process.env.CONFIRM_DUPLICATE_FIX !== 'true') {
        console.log('❌ Fix cancelled. Set CONFIRM_DUPLICATE_FIX=true to proceed.');
        process.exit(1);
      }
    }
    
    console.log('\n🔧 Step 2: Fixing duplicate subscriptions...');
    console.log('-'.repeat(50));
    
    let totalFixed = 0;
    let totalErrors = 0;
    
    for (const user of duplicateUsers) {
      try {
        const subscriptionIds = user.subscription_ids.split(',');
        const newestSubscriptionId = subscriptionIds[0];
        const oldSubscriptionIds = subscriptionIds.slice(1);
        
        // Mark old subscriptions as 'superseded'
        for (const oldId of oldSubscriptionIds) {
          const { error: updateError } = await supabase
            .from('user_subscriptions')
            .update({
              status: 'superseded',
              updated_at: new Date().toISOString()
            })
            .eq('id', oldId);

          if (updateError) {
            throw new Error(`Failed to update subscription ${oldId}: ${updateError.message}`);
          }

          console.log(`   ✅ ${user.username}: Marked subscription ${oldId} as superseded`);
          totalFixed++;
        }
        
      } catch (error) {
        console.log(`   ❌ ${user.username}: Error fixing subscriptions - ${error.message}`);
        totalErrors++;
      }
    }
    
    console.log('\n📊 Step 3: Verification...');
    console.log('-'.repeat(30));
    
    // Verify no more duplicates exist
    const { data: activeSubscriptions, error: verifyError } = await supabase
      .from('user_subscriptions')
      .select('user_id')
      .eq('status', 'active');

    if (verifyError) {
      throw new Error(`Failed to verify duplicates: ${verifyError.message}`);
    }

    // Count subscriptions per user
    const userCounts = {};
    activeSubscriptions.forEach(sub => {
      userCounts[sub.user_id] = (userCounts[sub.user_id] || 0) + 1;
    });

    const remainingDuplicates = Object.entries(userCounts)
      .filter(([userId, count]) => count > 1)
      .map(([userId, count]) => ({ user_id: userId, count }));
    
    if (remainingDuplicates.length === 0) {
      console.log('✅ All duplicate active subscriptions have been resolved!');
    } else {
      console.log(`⚠️  ${remainingDuplicates.length} users still have duplicate subscriptions`);
    }
    
    console.log('\n🎉 Fix completed successfully!');
    console.log('='.repeat(60));
    
    console.log('\n📋 SUMMARY:');
    console.log(`   • Users with duplicates: ${duplicateUsers.length}`);
    console.log(`   • Subscriptions fixed: ${totalFixed}`);
    console.log(`   • Errors encountered: ${totalErrors}`);
    console.log(`   • Remaining duplicates: ${remainingDuplicates.length}`);
    
    process.exit(0);
    
  } catch (error) {
    console.error('\n❌ Fix failed with error:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Fix interrupted by user');
  process.exit(1);
});

// Run the fix
fixDuplicateSubscriptions();
