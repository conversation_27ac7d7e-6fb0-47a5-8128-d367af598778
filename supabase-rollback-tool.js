#!/usr/bin/env node

/**
 * Supabase Migration Rollback Tool for StreamOnPod
 * 
 * This tool provides safe rollback capabilities if the Supabase migration
 * encounters issues or needs to be reverted.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class SupabaseRollbackTool {
  constructor() {
    this.backupDir = null;
    this.sqliteBackupPath = null;
    this.rollbackLogPath = './supabase_rollback_log.txt';
    this.log = [];
  }

  async executeRollback() {
    console.log('🔄 StreamOnPod Supabase Migration Rollback\n');
    
    try {
      // Step 1: Find backup files
      await this.findBackupFiles();
      
      // Step 2: Verify backup integrity
      await this.verifyBackups();
      
      // Step 3: Stop application
      await this.stopApplication();
      
      // Step 4: Restore SQLite database
      await this.restoreSQLiteDatabase();
      
      // Step 5: Restore application code
      await this.restoreApplicationCode();
      
      // Step 6: Restore dependencies
      await this.restoreDependencies();
      
      // Step 7: Verify rollback
      await this.verifyRollback();
      
      this.logMessage('✅ Rollback completed successfully!');
      this.logMessage('🚀 You can now restart the application with SQLite');
      
      // Save rollback log
      fs.writeFileSync(this.rollbackLogPath, this.log.join('\n'));
      console.log(`\n📋 Rollback log saved to: ${this.rollbackLogPath}`);
      
    } catch (error) {
      this.logMessage(`❌ Rollback failed: ${error.message}`);
      console.error('Rollback failed:', error);
    }
  }

  logMessage(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    this.log.push(logEntry);
    console.log(message);
  }

  async findBackupFiles() {
    this.logMessage('\n🔍 Step 1: Finding backup files...');
    
    try {
      // Find SQLite backup
      const dbBackups = fs.readdirSync('./db/')
        .filter(file => file.includes('streamonpod_pre_supabase_'))
        .sort()
        .reverse(); // Get most recent first

      if (dbBackups.length === 0) {
        throw new Error('No SQLite backup files found');
      }

      this.sqliteBackupPath = `./db/${dbBackups[0]}`;
      this.logMessage(`✅ Found SQLite backup: ${this.sqliteBackupPath}`);

      // Find application backup
      const appBackups = fs.readdirSync('./')
        .filter(dir => dir.includes('migration_backup_'))
        .sort()
        .reverse(); // Get most recent first

      if (appBackups.length === 0) {
        throw new Error('No application backup directories found');
      }

      this.backupDir = `./${appBackups[0]}`;
      this.logMessage(`✅ Found application backup: ${this.backupDir}`);

    } catch (error) {
      throw new Error(`Backup file discovery failed: ${error.message}`);
    }
  }

  async verifyBackups() {
    this.logMessage('\n🔍 Step 2: Verifying backup integrity...');
    
    try {
      // Verify SQLite backup
      if (!fs.existsSync(this.sqliteBackupPath)) {
        throw new Error(`SQLite backup not found: ${this.sqliteBackupPath}`);
      }

      const sqliteStats = fs.statSync(this.sqliteBackupPath);
      if (sqliteStats.size === 0) {
        throw new Error('SQLite backup file is empty');
      }

      this.logMessage(`✅ SQLite backup verified: ${Math.round(sqliteStats.size / 1024)}KB`);

      // Verify application backup
      if (!fs.existsSync(this.backupDir)) {
        throw new Error(`Application backup not found: ${this.backupDir}`);
      }

      const criticalFiles = ['app.js', 'package.json', 'db/database.js'];
      for (const file of criticalFiles) {
        const filePath = path.join(this.backupDir, file);
        if (!fs.existsSync(filePath)) {
          throw new Error(`Critical backup file missing: ${file}`);
        }
      }

      this.logMessage('✅ Application backup verified');

    } catch (error) {
      throw new Error(`Backup verification failed: ${error.message}`);
    }
  }

  async stopApplication() {
    this.logMessage('\n🛑 Step 3: Stopping application...');
    
    try {
      // Try to stop any running Node.js processes
      try {
        execSync('pkill -f "node app.js"', { stdio: 'ignore' });
        this.logMessage('✅ Stopped running application processes');
      } catch (error) {
        this.logMessage('⚠️  No running application processes found');
      }

      // Wait a moment for processes to stop
      await new Promise(resolve => setTimeout(resolve, 2000));

    } catch (error) {
      this.logMessage(`⚠️  Application stop warning: ${error.message}`);
    }
  }

  async restoreSQLiteDatabase() {
    this.logMessage('\n💾 Step 4: Restoring SQLite database...');
    
    try {
      // Backup current database (in case rollback fails)
      const currentDbPath = './db/streamonpod.db';
      const rollbackBackupPath = `./db/streamonpod_pre_rollback_${Date.now()}.db`;
      
      if (fs.existsSync(currentDbPath)) {
        fs.copyFileSync(currentDbPath, rollbackBackupPath);
        this.logMessage(`✅ Created rollback safety backup: ${rollbackBackupPath}`);
      }

      // Restore SQLite database
      fs.copyFileSync(this.sqliteBackupPath, currentDbPath);
      this.logMessage('✅ SQLite database restored');

      // Remove Supabase-related database files if they exist
      const supabaseFiles = [
        './supabase_schema.sql',
        './supabase_data.sql',
        './supabase-database-adapter.js'
      ];

      supabaseFiles.forEach(file => {
        if (fs.existsSync(file)) {
          fs.unlinkSync(file);
          this.logMessage(`✅ Removed Supabase file: ${file}`);
        }
      });

    } catch (error) {
      throw new Error(`SQLite database restoration failed: ${error.message}`);
    }
  }

  async restoreApplicationCode() {
    this.logMessage('\n📁 Step 5: Restoring application code...');
    
    try {
      // Restore critical application files
      const filesToRestore = [
        'app.js',
        'db/database.js',
        'models/',
        'middleware/'
      ];

      filesToRestore.forEach(file => {
        const sourcePath = path.join(this.backupDir, file);
        const destPath = file;

        if (fs.existsSync(sourcePath)) {
          if (fs.statSync(sourcePath).isDirectory()) {
            this.copyDirectory(sourcePath, destPath);
          } else {
            // Create directory if it doesn't exist
            const destDir = path.dirname(destPath);
            if (!fs.existsSync(destDir)) {
              fs.mkdirSync(destDir, { recursive: true });
            }
            fs.copyFileSync(sourcePath, destPath);
          }
          this.logMessage(`✅ Restored: ${file}`);
        }
      });

    } catch (error) {
      throw new Error(`Application code restoration failed: ${error.message}`);
    }
  }

  copyDirectory(src, dest) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    
    const files = fs.readdirSync(src);
    files.forEach(file => {
      const srcPath = path.join(src, file);
      const destPath = path.join(dest, file);
      
      if (fs.statSync(srcPath).isDirectory()) {
        this.copyDirectory(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    });
  }

  async restoreDependencies() {
    this.logMessage('\n📦 Step 6: Restoring dependencies...');
    
    try {
      // Restore package.json
      const packageJsonPath = path.join(this.backupDir, 'package.json');
      if (fs.existsSync(packageJsonPath)) {
        fs.copyFileSync(packageJsonPath, './package.json');
        this.logMessage('✅ Restored package.json');
      }

      // Remove Supabase dependencies
      try {
        execSync('npm uninstall @supabase/supabase-js @supabase/cli', { stdio: 'ignore' });
        this.logMessage('✅ Removed Supabase dependencies');
      } catch (error) {
        this.logMessage('⚠️  Could not remove Supabase dependencies (may not be installed)');
      }

      // Reinstall original dependencies
      try {
        execSync('npm install', { stdio: 'ignore' });
        this.logMessage('✅ Reinstalled original dependencies');
      } catch (error) {
        this.logMessage(`⚠️  Dependency installation warning: ${error.message}`);
      }

    } catch (error) {
      throw new Error(`Dependency restoration failed: ${error.message}`);
    }
  }

  async verifyRollback() {
    this.logMessage('\n✅ Step 7: Verifying rollback...');
    
    try {
      // Check if SQLite database exists and is accessible
      const sqlite3 = require('sqlite3').verbose();
      const db = new sqlite3.Database('./db/streamonpod.db', sqlite3.OPEN_READONLY, (err) => {
        if (err) {
          throw new Error(`SQLite database verification failed: ${err.message}`);
        }
      });

      // Test a simple query
      await new Promise((resolve, reject) => {
        db.get('SELECT COUNT(*) as count FROM users', (err, row) => {
          if (err) {
            reject(new Error(`Database query test failed: ${err.message}`));
          } else {
            this.logMessage(`✅ Database verification: ${row.count} users found`);
            resolve();
          }
        });
        db.close();
      });

      // Check if critical files exist
      const criticalFiles = ['app.js', 'db/database.js', 'package.json'];
      criticalFiles.forEach(file => {
        if (!fs.existsSync(file)) {
          throw new Error(`Critical file missing after rollback: ${file}`);
        }
      });

      this.logMessage('✅ All critical files verified');

    } catch (error) {
      throw new Error(`Rollback verification failed: ${error.message}`);
    }
  }

  // Interactive rollback with confirmation
  async interactiveRollback() {
    console.log('🔄 StreamOnPod Supabase Migration Rollback Tool\n');
    console.log('⚠️  WARNING: This will revert your application to use SQLite');
    console.log('⚠️  All Supabase-related changes will be undone');
    console.log('⚠️  Make sure you have stopped the application before proceeding\n');

    // In a real implementation, you would use readline for user input
    // For now, we'll proceed automatically
    console.log('🚀 Proceeding with automatic rollback...\n');
    
    await this.executeRollback();
  }
}

// Utility function to clean up migration files
function cleanupMigrationFiles() {
  console.log('🧹 Cleaning up migration files...\n');
  
  const migrationFiles = [
    'supabase-migration-executor.js',
    'supabase-database-adapter.js',
    'supabase-query-examples.js',
    'supabase-migration-tests.js',
    'supabase-rollback-tool.js',
    'SUPABASE_MIGRATION_INSTRUCTIONS.md',
    'supabase_schema.sql',
    'supabase_data.sql',
    'supabase_migration_log.txt'
  ];

  let cleaned = 0;
  migrationFiles.forEach(file => {
    if (fs.existsSync(file)) {
      fs.unlinkSync(file);
      console.log(`✅ Removed: ${file}`);
      cleaned++;
    }
  });

  console.log(`\n✅ Cleaned up ${cleaned} migration files`);
}

// Command line interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--cleanup')) {
    cleanupMigrationFiles();
  } else if (args.includes('--interactive')) {
    const rollback = new SupabaseRollbackTool();
    rollback.interactiveRollback().catch(console.error);
  } else {
    const rollback = new SupabaseRollbackTool();
    rollback.executeRollback().catch(console.error);
  }
}

module.exports = { SupabaseRollbackTool, cleanupMigrationFiles };
