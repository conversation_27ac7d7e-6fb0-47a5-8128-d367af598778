#!/usr/bin/env node

/**
 * Debug PodLite Streaming Limit Issue
 * Comprehensive diagnostic tool to identify and resolve PodLite subscription issues
 */

const { supabase } = require('./db/database');
const Subscription = require('./models/Subscription');

class PodLiteDebugger {
  constructor() {
    this.stats = {
      totalUsers: 0,
      podliteUsers: 0,
      issuesFound: 0,
      issuesFixed: 0
    };
  }

  async run() {
    try {
      console.log('🔍 StreamOnPod: PodLite Streaming Limit Diagnostic');
      console.log('='.repeat(60));
      console.log(`Time: ${new Date().toISOString()}\n`);

      // Step 1: Check subscription plans configuration
      await this.checkSubscriptionPlans();
      
      // Step 2: Check for duplicate subscriptions
      await this.checkDuplicateSubscriptions();
      
      // Step 3: Check PodLite users specifically
      await this.checkPodLiteUsers();
      
      // Step 4: Test quota logic
      await this.testQuotaLogic();
      
      // Step 5: Summary and recommendations
      await this.printSummary();

    } catch (error) {
      console.error('❌ Debug failed:', error.message);
      console.error('Stack trace:', error.stack);
      process.exit(1);
    }
  }

  async checkSubscriptionPlans() {
    console.log('📋 Step 1: Checking Subscription Plans Configuration');
    console.log('-'.repeat(50));

    try {
      const { data: plans, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .order('name');

      if (error) {
        throw new Error(`Failed to fetch subscription plans: ${error.message}`);
      }

      console.log(`Found ${plans.length} subscription plans:`);

      let podlitePlan = null;
      for (const plan of plans) {
        const isActive = plan.is_active ? '✅' : '❌';
        console.log(`  ${isActive} ${plan.name}: ${plan.max_streaming_slots} slots, ${plan.max_storage_gb}GB, $${plan.price/100}`);

        if (plan.name.toLowerCase() === 'podlite') {
          podlitePlan = plan;
        }
      }

      if (!podlitePlan) {
        console.log('❌ PodLite plan not found!');
        return false;
      }

      if (podlitePlan.max_streaming_slots !== 1) {
        console.log(`⚠️  PodLite plan has incorrect streaming slots: ${podlitePlan.max_streaming_slots} (should be 1)`);
        return false;
      }

      console.log('✅ PodLite plan configuration is correct\n');
      return true;
    } catch (error) {
      console.error('❌ Error checking subscription plans:', error.message);
      return false;
    }
  }

  async checkDuplicateSubscriptions() {
    console.log('🔍 Step 2: Checking for Duplicate Active Subscriptions');
    console.log('-'.repeat(50));

    try {
      // Get all active subscriptions with user and plan info
      const { data: subscriptions, error } = await supabase
        .from('user_subscriptions')
        .select(`
          user_id,
          users!inner (username),
          subscription_plans!inner (name)
        `)
        .eq('status', 'active');

      if (error) {
        throw new Error(`Failed to fetch active subscriptions: ${error.message}`);
      }

      // Group by user_id to find duplicates
      const userSubscriptions = {};
      subscriptions.forEach(sub => {
        if (!userSubscriptions[sub.user_id]) {
          userSubscriptions[sub.user_id] = {
            username: sub.users.username,
            plans: [],
            count: 0
          };
        }
        userSubscriptions[sub.user_id].plans.push(sub.subscription_plans.name);
        userSubscriptions[sub.user_id].count++;
      });

      // Find users with multiple active subscriptions
      const duplicates = Object.entries(userSubscriptions)
        .filter(([userId, data]) => data.count > 1)
        .map(([userId, data]) => ({
          user_id: userId,
          username: data.username,
          active_count: data.count,
          plan_names: data.plans.join(', ')
        }));

      if (duplicates.length === 0) {
        console.log('✅ No duplicate active subscriptions found\n');
        return true;
      }

      console.log(`⚠️  Found ${duplicates.length} users with duplicate subscriptions:`);
      for (const dup of duplicates) {
        console.log(`  - ${dup.username}: ${dup.active_count} active (${dup.plan_names})`);
        this.stats.issuesFound++;
      }
      console.log('');
      return false;
    } catch (error) {
      console.error('❌ Error checking duplicate subscriptions:', error.message);
      return false;
    }
  }

  async checkPodLiteUsers() {
    console.log('👥 Step 3: Checking PodLite Users');
    console.log('-'.repeat(50));

    try {
      // Get PodLite users with active subscriptions
      const { data: users, error } = await supabase
        .from('users')
        .select(`
          id,
          username,
          user_subscriptions!inner (
            status,
            end_date,
            subscription_plans!inner (
              name,
              max_streaming_slots
            )
          )
        `)
        .eq('user_subscriptions.subscription_plans.name', 'PodLite')
        .eq('user_subscriptions.status', 'active')
        .order('username');

      if (error) {
        throw new Error(`Failed to fetch PodLite users: ${error.message}`);
      }

      // Get stream counts for each user
      const podliteUsers = await Promise.all((users || []).map(async (user) => {
        const { count: streamCount, error: countError } = await supabase
          .from('streams')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.id);

        if (countError) {
          console.warn(`Warning: Could not get stream count for user ${user.username}`);
        }

        const subscription = user.user_subscriptions[0];
        const plan = subscription.subscription_plans;

        return {
          id: user.id,
          username: user.username,
          subscription_status: subscription.status,
          plan_name: plan.name,
          max_streaming_slots: plan.max_streaming_slots,
          end_date: subscription.end_date,
          stream_count: streamCount || 0
        };
      }));

      this.stats.podliteUsers = podliteUsers.length;
      console.log(`Found ${podliteUsers.length} active PodLite users:`);

      if (podliteUsers.length === 0) {
        console.log('ℹ️  No active PodLite users found\n');
        return true;
      }

      for (const user of podliteUsers) {
        const status = user.subscription_status === 'active' ? '✅' : '❌';
        const expiry = user.end_date ? new Date(user.end_date).toLocaleDateString() : 'No expiry';

        console.log(`  ${status} ${user.username}:`);
        console.log(`      Plan: ${user.plan_name} (${user.max_streaming_slots} slots)`);
        console.log(`      Streams: ${user.stream_count}`);
        console.log(`      Expires: ${expiry}`);

        // Test quota for this user
        try {
          const quotaCheck = await Subscription.checkStreamingSlotLimit(user.id);
          const quotaStatus = quotaCheck.hasLimit ? '🚫 BLOCKED' : '✅ ALLOWED';
          console.log(`      Quota: ${quotaStatus} (${quotaCheck.currentSlots}/${quotaCheck.maxSlots})`);

          if (quotaCheck.hasLimit && quotaCheck.currentSlots < quotaCheck.maxSlots) {
            console.log(`      ⚠️  ISSUE: User blocked but has available slots!`);
            this.stats.issuesFound++;
          }
        } catch (error) {
          console.log(`      ❌ Error checking quota: ${error.message}`);
          this.stats.issuesFound++;
        }
        console.log('');
      }

      return this.stats.issuesFound === 0;
    } catch (error) {
      console.error('❌ Error checking PodLite users:', error.message);
      return false;
    }
  }

  async testQuotaLogic() {
    console.log('🧪 Step 4: Testing Quota Logic');
    console.log('-'.repeat(50));

    try {
      // Test with a sample PodLite user if available
      const { data: users, error } = await supabase
        .from('users')
        .select(`
          id,
          username,
          user_subscriptions!inner (
            status,
            subscription_plans!inner (
              name
            )
          )
        `)
        .eq('user_subscriptions.subscription_plans.name', 'PodLite')
        .eq('user_subscriptions.status', 'active')
        .limit(1);

      if (error) {
        throw new Error(`Failed to fetch sample PodLite user: ${error.message}`);
      }

      const sampleUser = users && users.length > 0 ? users[0] : null;

      if (!sampleUser) {
        console.log('ℹ️  No PodLite users available for testing\n');
        return true;
      }

      console.log(`Testing with user: ${sampleUser.username}`);

      try {
        const quotaCheck = await Subscription.checkStreamingSlotLimit(sampleUser.id);

        console.log('Quota check result:');
        console.log(`  - Has limit: ${quotaCheck.hasLimit}`);
        console.log(`  - Max slots: ${quotaCheck.maxSlots}`);
        console.log(`  - Current slots: ${quotaCheck.currentSlots}`);
        console.log(`  - Plan source: ${quotaCheck.planSource || 'N/A'}`);
        console.log(`  - Subscription ID: ${quotaCheck.subscriptionId || 'N/A'}`);

        // Verify logic
        const expectedHasLimit = quotaCheck.maxSlots !== -1 && quotaCheck.currentSlots >= quotaCheck.maxSlots;
        if (quotaCheck.hasLimit !== expectedHasLimit) {
          console.log(`⚠️  Logic error: hasLimit=${quotaCheck.hasLimit}, expected=${expectedHasLimit}`);
          this.stats.issuesFound++;
        } else {
          console.log('✅ Quota logic working correctly');
        }

      } catch (error) {
        console.log(`❌ Error testing quota logic: ${error.message}`);
        this.stats.issuesFound++;
      }

      console.log('');
      return this.stats.issuesFound === 0;
    } catch (error) {
      console.error('❌ Error in quota logic test:', error.message);
      return false;
    }
  }

  async printSummary() {
    console.log('📊 DIAGNOSTIC SUMMARY');
    console.log('='.repeat(60));
    console.log(`PodLite Users Found: ${this.stats.podliteUsers}`);
    console.log(`Issues Identified: ${this.stats.issuesFound}`);
    console.log(`Issues Fixed: ${this.stats.issuesFixed}`);

    if (this.stats.issuesFound === 0) {
      console.log('\n✅ No issues found! PodLite streaming limits are working correctly.');
    } else {
      console.log('\n⚠️  Issues found that need attention:');
      console.log('   1. Check for duplicate subscriptions');
      console.log('   2. Verify subscription plan configuration');
      console.log('   3. Test quota logic with affected users');
      console.log('\nRecommended actions:');
      console.log('   - Run: node scripts/fix-duplicate-subscriptions.js');
      console.log('   - Run: node scripts/fix-preview-plan.js');
      console.log('   - Check database integrity');
    }

    console.log('\n' + '='.repeat(60));
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Diagnostic interrupted by user');
  process.exit(1);
});

// Run the diagnostic
const diagnostic = new PodLiteDebugger();
diagnostic.run().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
