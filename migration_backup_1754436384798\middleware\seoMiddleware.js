const seoService = require('../services/seoService');

// SEO middleware to add SEO data to all responses
const seoMiddleware = (req, res, next) => {
  // Store original render function
  const originalRender = res.render;
  
  // Override render function to inject SEO data
  res.render = function(view, locals = {}, callback) {
    // Don't add SEO to API responses or error pages
    if (req.path.startsWith('/api/') || view === 'error') {
      return originalRender.call(this, view, locals, callback);
    }

    // Determine page type and generate appropriate SEO data
    const seoOptions = getSEOOptionsForPage(req, locals);
    const seoData = seoService.generatePageSEO(seoOptions);

    // Merge SEO data with existing locals
    const enhancedLocals = {
      ...locals,
      seo: {
        ...seoData,
        // Helper function to render JSON-LD scripts
        renderJsonLd: function() {
          if (!seoData.jsonLd || seoData.jsonLd.length === 0) return '';
          
          return seoData.jsonLd.map(schema => 
            `<script type="application/ld+json">${JSON.stringify(schema, null, 2)}</script>`
          ).join('\n');
        },
        // Helper function to render meta tags
        renderMetaTags: function() {
          let metaTags = '';
          
          // Open Graph tags
          if (seoData.openGraph) {
            Object.entries(seoData.openGraph).forEach(([property, content]) => {
              metaTags += `<meta property="${property}" content="${content}">\n`;
            });
          }
          
          // Twitter Card tags
          if (seoData.twitterCard) {
            Object.entries(seoData.twitterCard).forEach(([name, content]) => {
              metaTags += `<meta name="${name}" content="${content}">\n`;
            });
          }
          
          // Canonical URL
          if (seoData.canonical) {
            metaTags += `<link rel="canonical" href="${seoData.canonical}">\n`;
          }
          
          return metaTags;
        }
      }
    };

    return originalRender.call(this, view, enhancedLocals, callback);
  };

  next();
};

// Determine SEO options based on the current page
function getSEOOptionsForPage(req, locals) {
  const path = req.path;
  const locale = locals.locale || 'id'; // Default to Indonesian
  const baseOptions = {
    path: path,
    includeOrganization: true,
    locale: locale
  };

  // Landing page
  if (path === '/') {
    const title = locale === 'en'
      ? 'StreamOnPod - Cloud Streaming Platform for Automated Broadcasting'
      : 'StreamOnPod - Platform Streaming Cloud untuk Siaran Otomatis';
    const description = locale === 'en'
      ? 'Transform your videos into automated live streams with StreamOnPod. Cloud-powered streaming platform for continuous content broadcasting across multiple platforms with enterprise-grade infrastructure.'
      : 'Ubah video Anda menjadi live stream otomatis dengan StreamOnPod. Platform streaming berbasis cloud untuk siaran konten berkelanjutan di berbagai platform dengan infrastruktur tingkat enterprise.';
    const homeName = locale === 'en' ? 'Home' : 'Beranda';

    return {
      ...baseOptions,
      title: title,
      description: description,
      includeSoftwareApp: true,
      includeWebSite: true,
      breadcrumbs: [
        { name: homeName, url: '/' }
      ]
    };
  }

  // Login page
  if (path === '/login') {
    const title = locale === 'en' ? 'Login - StreamOnPod' : 'Masuk - StreamOnPod';
    const description = locale === 'en'
      ? 'Login to your StreamOnPod account to access cloud streaming platform and manage your automated broadcasts.'
      : 'Masuk ke akun StreamOnPod Anda untuk mengakses platform streaming cloud dan mengelola siaran otomatis Anda.';
    const homeName = locale === 'en' ? 'Home' : 'Beranda';
    const loginName = locale === 'en' ? 'Login' : 'Masuk';

    return {
      ...baseOptions,
      title: title,
      description: description,
      breadcrumbs: [
        { name: homeName, url: '/' },
        { name: loginName, url: '/login' }
      ]
    };
  }

  // Register page
  if (path === '/register') {
    const title = locale === 'en' ? 'Register - StreamOnPod' : 'Daftar - StreamOnPod';
    const description = locale === 'en'
      ? 'Create your StreamOnPod account and start streaming with our cloud-powered platform. Free preview plan available.'
      : 'Buat akun StreamOnPod Anda dan mulai streaming dengan platform berbasis cloud kami. Paket preview gratis tersedia.';
    const homeName = locale === 'en' ? 'Home' : 'Beranda';
    const registerName = locale === 'en' ? 'Register' : 'Daftar';

    return {
      ...baseOptions,
      title: title,
      description: description,
      breadcrumbs: [
        { name: homeName, url: '/' },
        { name: registerName, url: '/register' }
      ]
    };
  }

  // Subscription page
  if (path === '/subscription') {
    const title = locale === 'en' ? 'Subscription Plans - StreamOnPod' : 'Paket Berlangganan - StreamOnPod';
    const description = locale === 'en'
      ? 'Choose the perfect streaming plan for your needs. From Preview to PodPrime, find the right solution for your broadcasting requirements.'
      : 'Pilih paket streaming yang sempurna untuk kebutuhan Anda. Dari Preview hingga PodPrime, temukan solusi yang tepat untuk kebutuhan siaran Anda.';
    const homeName = locale === 'en' ? 'Home' : 'Beranda';
    const subscriptionName = locale === 'en' ? 'Subscription' : 'Berlangganan';

    return {
      ...baseOptions,
      title: title,
      description: description,
      plan: locals.plans && locals.plans.length > 0 ? locals.plans.find(p => p.name !== 'Preview') || locals.plans[0] : null,
      breadcrumbs: [
        { name: homeName, url: '/' },
        { name: subscriptionName, url: '/subscription' }
      ]
    };
  }

  // Dashboard
  if (path === '/dashboard') {
    const title = locale === 'en' ? 'Dashboard - StreamOnPod' : 'Dasbor - StreamOnPod';
    const description = locale === 'en'
      ? 'Manage your streams, videos, and broadcasting settings from your StreamOnPod dashboard.'
      : 'Kelola stream, video, dan pengaturan siaran Anda dari dasbor StreamOnPod.';
    const homeName = locale === 'en' ? 'Home' : 'Beranda';
    const dashboardName = locale === 'en' ? 'Dashboard' : 'Dasbor';

    return {
      ...baseOptions,
      title: title,
      description: description,
      breadcrumbs: [
        { name: homeName, url: '/' },
        { name: dashboardName, url: '/dashboard' }
      ]
    };
  }

  // Gallery
  if (path === '/gallery') {
    return {
      ...baseOptions,
      title: 'Video Gallery - StreamOnPod',
      description: 'Browse and manage your video library for streaming on StreamOnPod platform.',
      video: locals.videos && locals.videos.length > 0 ? locals.videos[0] : null, // Use first video for schema
      breadcrumbs: [
        { name: 'Home', url: '/' },
        { name: 'Dashboard', url: '/dashboard' },
        { name: 'Gallery', url: '/gallery' }
      ]
    };
  }

  // History
  if (path === '/history') {
    return {
      ...baseOptions,
      title: 'Streaming History - StreamOnPod',
      description: 'View your streaming history and analytics on StreamOnPod platform.',
      breadcrumbs: [
        { name: 'Home', url: '/' },
        { name: 'Dashboard', url: '/dashboard' },
        { name: 'History', url: '/history' }
      ]
    };
  }

  // Settings
  if (path === '/settings') {
    return {
      ...baseOptions,
      title: 'Settings - StreamOnPod',
      description: 'Configure your StreamOnPod account settings and preferences.',
      breadcrumbs: [
        { name: 'Home', url: '/' },
        { name: 'Dashboard', url: '/dashboard' },
        { name: 'Settings', url: '/settings' }
      ]
    };
  }

  // Privacy Policy
  if (path === '/privacy-policy') {
    return {
      ...baseOptions,
      title: 'Privacy Policy - StreamOnPod',
      description: 'Read StreamOnPod privacy policy and learn how we protect your data and privacy.',
      breadcrumbs: [
        { name: 'Home', url: '/' },
        { name: 'Privacy Policy', url: '/privacy-policy' }
      ]
    };
  }

  // Terms of Service
  if (path === '/tos') {
    return {
      ...baseOptions,
      title: 'Terms of Service - StreamOnPod',
      description: 'Read StreamOnPod terms of service and understand our platform usage guidelines.',
      breadcrumbs: [
        { name: 'Home', url: '/' },
        { name: 'Terms of Service', url: '/tos' }
      ]
    };
  }

  // Default fallback
  return {
    ...baseOptions,
    title: `${locals.title || 'StreamOnPod'} - Cloud Streaming Platform`,
    description: 'StreamOnPod - Cloud-powered streaming platform for continuous content broadcasting across multiple platforms.',
    breadcrumbs: [
      { name: 'Home', url: '/' }
    ]
  };
}

module.exports = seoMiddleware;
