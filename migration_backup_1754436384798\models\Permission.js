const { db } = require('../db/database');

class Permission {
  // Get all permissions for a role
  static getPermissionsByRole(role) {
    return new Promise((resolve, reject) => {
      db.all('SELECT permission FROM role_permissions WHERE role = ?', [role], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows.map(row => row.permission));
      });
    });
  }

  // Check if user has specific permission
  static async hasPermission(userId, permission) {
    try {
      const user = await new Promise((resolve, reject) => {
        db.get('SELECT role FROM users WHERE id = ?', [userId], (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });

      if (!user) return false;

      const permissions = await this.getPermissionsByRole(user.role);
      return permissions.includes(permission);
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }

  // Middleware to check permissions
  static requirePermission(permission) {
    return async (req, res, next) => {
      try {
        if (!req.session.userId) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        const hasPermission = await Permission.hasPermission(req.session.userId, permission);
        if (!hasPermission) {
          return res.status(403).json({ error: 'Insufficient permissions' });
        }

        next();
      } catch (error) {
        console.error('Permission middleware error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    };
  }

  // Middleware to check role
  static requireRole(role) {
    return async (req, res, next) => {
      try {
        console.log('🔐 Role middleware called for role:', role);
        console.log('🔐 Session userId:', req.session.userId);
        if (!req.session.userId) {
          console.log('❌ No userId in session');
          return res.status(401).json({ error: 'Authentication required' });
        }

        const user = await new Promise((resolve, reject) => {
          db.get('SELECT role FROM users WHERE id = ?', [req.session.userId], (err, row) => {
            if (err) reject(err);
            else resolve(row);
          });
        });

        console.log('🔐 User from database:', user);
        console.log('🔐 Required role:', role, 'User role:', user?.role);
        if (!user || user.role !== role) {
          console.log('❌ Role check failed');
          return res.status(403).json({ error: 'Insufficient role privileges' });
        }

        console.log('✅ Role check passed');
        next();
      } catch (error) {
        console.error('Role middleware error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    };
  }

  // Check if user is admin
  static async isAdmin(userId) {
    try {
      const user = await new Promise((resolve, reject) => {
        db.get('SELECT role FROM users WHERE id = ?', [userId], (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });

      return user && user.role === 'admin';
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  }

  // Get all roles
  static getAllRoles() {
    return new Promise((resolve, reject) => {
      db.all('SELECT DISTINCT role FROM role_permissions ORDER BY role', [], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows.map(row => row.role));
      });
    });
  }

  // Get all permissions
  static getAllPermissions() {
    return new Promise((resolve, reject) => {
      db.all('SELECT DISTINCT permission FROM role_permissions ORDER BY permission', [], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows.map(row => row.permission));
      });
    });
  }

  // Add permission to role
  static addPermissionToRole(role, permission) {
    const { v4: uuidv4 } = require('uuid');
    return new Promise((resolve, reject) => {
      db.run(
        'INSERT OR IGNORE INTO role_permissions (id, role, permission) VALUES (?, ?, ?)',
        [uuidv4(), role, permission],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ role, permission });
        }
      );
    });
  }

  // Remove permission from role
  static removePermissionFromRole(role, permission) {
    return new Promise((resolve, reject) => {
      db.run(
        'DELETE FROM role_permissions WHERE role = ? AND permission = ?',
        [role, permission],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ role, permission, removed: this.changes > 0 });
        }
      );
    });
  }
}

module.exports = Permission;
