[2025-08-05T23:19:47.124Z] Created recovery backup: ./db/streamonpod_pre_manual_recovery_1754435987122.db
[2025-08-05T23:19:47.136Z] Found 93 users in backup
[2025-08-05T23:19:47.140Z] Found 1 users in current database
[2025-08-05T23:19:47.141Z] Need to recover 93 missing users
[2025-08-05T23:19:47.250Z] ✅ Users recovery complete: 0/93 recovered
[2025-08-05T23:19:47.263Z] Found 5 subscription plans in backup
[2025-08-05T23:19:47.272Z] ✅ Subscription plans recovery complete: 0/5 recovered
[2025-08-05T23:19:47.288Z] Found 28 videos in backup
[2025-08-05T23:19:47.327Z] ✅ Videos recovery complete: 0/28 recovered
[2025-08-05T23:19:47.342Z] Found 16 streams in backup
[2025-08-05T23:19:47.369Z] ✅ Streams recovery complete: 0/16 recovered
[2025-08-05T23:19:47.709Z] Found 3056 missing notifications to recover
[2025-08-05T23:19:48.095Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.096Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.096Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.097Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.097Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.097Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.098Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.098Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.098Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.099Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.099Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.099Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.100Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.100Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.100Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.100Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.101Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.103Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.104Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.104Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.105Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.105Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.105Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.105Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.106Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.106Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.111Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.111Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.111Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.112Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.113Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.113Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.113Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.114Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.114Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.115Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.115Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.115Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.116Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.116Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.116Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.116Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.117Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.117Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.117Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.117Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.118Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.118Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.118Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.118Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.118Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.119Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.119Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.119Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.120Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.121Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.121Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.121Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.121Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.122Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.122Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.129Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.130Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.131Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.132Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.133Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.133Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.134Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.134Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.134Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.135Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.135Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.136Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.137Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.139Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.143Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.144Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.144Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.145Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.145Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.148Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.149Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.150Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.151Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.152Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.154Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.161Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.162Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.162Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.163Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.163Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.163Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.164Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.164Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.165Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.166Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.166Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.167Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.168Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.169Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.170Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.174Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.175Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.176Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.177Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.177Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.178Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.179Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.180Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.180Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.181Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.181Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.181Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.182Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.182Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.182Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.182Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.183Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.183Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.184Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.185Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.185Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.186Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.191Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.191Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.193Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.194Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.195Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.196Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.196Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.197Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.198Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.199Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.199Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.200Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.201Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.201Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.202Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.206Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.207Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.208Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.209Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.210Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.210Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.211Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.211Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.212Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.212Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.213Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.213Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.214Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.214Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.215Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.215Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.217Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.217Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.223Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.223Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.224Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.225Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.225Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.226Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.226Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.227Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.227Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.228Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.229Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.229Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.230Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.230Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.230Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.231Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.231Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.232Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.233Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.233Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.239Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.240Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.241Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.241Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.242Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.243Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.245Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.246Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.247Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.250Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.256Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.256Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.257Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.257Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.258Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.258Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.259Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.259Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.260Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.260Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.261Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.261Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.261Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.262Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.262Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.262Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.263Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.263Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.264Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.265Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.270Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.270Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.271Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.271Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.272Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.273Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.273Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.273Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.274Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.275Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.275Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.276Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.276Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.276Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.277Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.277Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.278Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.279Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.279Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.280Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.281Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.287Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.288Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.288Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.290Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.291Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.293Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.293Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.294Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.295Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.296Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.297Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.303Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.304Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.305Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.305Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.306Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.306Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.306Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.306Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.307Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.307Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.307Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.308Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.308Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.308Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.309Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.309Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.310Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.310Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.311Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.312Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.312Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.315Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.321Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.321Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.322Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.322Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.322Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.322Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.323Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.323Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.323Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.323Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.324Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.324Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.324Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.325Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.325Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.326Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.326Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.327Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.328Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.329Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.335Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.335Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.336Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.337Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.338Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.339Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.339Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.340Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.340Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.341Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.342Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.343Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.344Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.346Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.351Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.352Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.353Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.353Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.354Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.354Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.354Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.355Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.355Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.355Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.355Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.356Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.356Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.356Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.356Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.357Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.357Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.357Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.358Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.358Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.359Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.360Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.360Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.361Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.366Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.366Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.366Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.367Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.367Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.368Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.369Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.369Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.370Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.370Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.370Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.371Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.371Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.371Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.371Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.372Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.372Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.372Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.373Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.373Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.373Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.373Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.374Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.374Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.374Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.375Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.376Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.376Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.377Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.383Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.384Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.385Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.386Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.387Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.387Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.388Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.388Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.389Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.389Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.390Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.390Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.391Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.391Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.392Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.394Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.398Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.398Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.399Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.399Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.399Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.400Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.401Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.401Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.401Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.402Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.402Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.402Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.403Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.403Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.403Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.404Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.404Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.404Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.404Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.405Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.405Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.405Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.405Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.406Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.406Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.407Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.408Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.409Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.414Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.414Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.414Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.414Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.415Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.415Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.415Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.416Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.417Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.417Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.417Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.418Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.418Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.418Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.418Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.418Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.419Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.419Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.419Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.419Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.420Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.420Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.421Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.421Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.421Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.422Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.422Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.422Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.422Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.423Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.423Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.423Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.424Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.424Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.424Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.429Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.429Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.432Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.433Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.434Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.434Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.435Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.435Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.435Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.436Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.438Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.479Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.487Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.487Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.488Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.489Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.490Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.491Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.491Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.492Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.493Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.494Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.494Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.494Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.495Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.495Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.495Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.495Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.495Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.496Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.496Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.496Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.496Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.497Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.497Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.497Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.497Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.498Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.498Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.498Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.498Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.499Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.499Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.499Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.499Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.499Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.500Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.500Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.500Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.500Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.500Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.501Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.501Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.502Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.503Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.504Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.504Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.511Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.514Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.514Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.515Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.515Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.515Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.516Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.516Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.516Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.516Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.517Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.517Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.517Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.517Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.518Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.518Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.518Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.518Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.519Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.519Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.519Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.519Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.520Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.520Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.520Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.520Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.521Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.521Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.522Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.522Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.523Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.525Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.526Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.527Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.527Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.528Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.529Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.529Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.529Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.530Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.530Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.530Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.531Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.531Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.532Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.532Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.533Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.533Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.533Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.534Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.534Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.534Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.535Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.535Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.535Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.536Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.541Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.541Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.542Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.542Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.543Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.543Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.544Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.544Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.544Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.545Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.545Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.545Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.545Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.546Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.546Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.546Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.547Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.547Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.547Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.547Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.548Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.548Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.548Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.548Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.548Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.549Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.549Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.549Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.549Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.549Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.550Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.550Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.550Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.550Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.551Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.551Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.551Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.551Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.551Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.552Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.556Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.556Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.557Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.557Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.557Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.558Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.558Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.559Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.559Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.560Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.560Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.561Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.561Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.561Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.561Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.562Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.562Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.562Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.562Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.562Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.563Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.563Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.563Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.563Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.563Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.564Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.564Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.564Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.564Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.564Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.565Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.565Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.565Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.565Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.565Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.566Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.566Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.566Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.566Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.566Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.567Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.567Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.567Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.567Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.567Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.568Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.572Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.572Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.572Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.573Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.574Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.574Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.576Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.577Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.578Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.578Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.578Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.579Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.579Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.579Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.580Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.580Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.580Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.581Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.581Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.581Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.582Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.582Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.583Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.583Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.583Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.584Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.589Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.589Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.590Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.590Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.590Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.590Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.590Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.591Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.591Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.592Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.592Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.593Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.593Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.594Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.594Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.594Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.594Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.595Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.595Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.595Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.595Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.596Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.596Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.596Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.597Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.597Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.597Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.597Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.597Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.598Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.598Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.598Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.598Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.598Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.599Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.599Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.599Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.603Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.604Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.604Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.604Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.604Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.604Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.605Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.605Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.605Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.605Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.605Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.606Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.606Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.606Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.606Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.606Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.607Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.607Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.607Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.608Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.608Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.609Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.609Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.610Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.610Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.610Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.611Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.611Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.612Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.612Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.612Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.612Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.612Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.613Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.613Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.613Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.613Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.613Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.614Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.614Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.614Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.614Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.619Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.619Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.619Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.621Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.621Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.621Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.622Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.622Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.622Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.623Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.623Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.623Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.624Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.624Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.625Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.626Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.627Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.627Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.628Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.628Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.629Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.629Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.630Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.631Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.636Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.636Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.636Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.636Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.637Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.637Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.637Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.637Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.637Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.638Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.638Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.638Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.638Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.638Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.639Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.639Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.639Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.639Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.640Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.640Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.640Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.640Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.641Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.641Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.642Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.642Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.643Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.643Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.643Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.644Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.644Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.644Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.645Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.645Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.645Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.646Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.646Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.646Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.651Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.651Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.651Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.651Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.651Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.652Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.652Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.652Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.652Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.652Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.653Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.653Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.653Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.653Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.653Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.654Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.654Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.654Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.654Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.654Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.655Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.655Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.655Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.655Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.656Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.656Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.656Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.656Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.656Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.657Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.657Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.657Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.658Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.658Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.659Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.659Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.660Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.660Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.661Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.661Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.661Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.661Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.662Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.666Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.666Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.667Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.667Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.668Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.669Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.670Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.670Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.670Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.671Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.671Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.671Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.672Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.672Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.672Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.673Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.673Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.674Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.675Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.676Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.677Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.684Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.684Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.685Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.685Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.685Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.685Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.685Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.686Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.686Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.686Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.686Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.686Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.687Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.687Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.687Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.687Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.688Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.688Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.688Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.688Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.689Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.689Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.689Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.689Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.689Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.690Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.690Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.690Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.690Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.690Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.691Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.692Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.692Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.697Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.697Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.698Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.698Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.698Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.698Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.698Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.699Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.699Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.699Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.699Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.699Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.700Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.700Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.700Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.700Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.701Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.701Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.701Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.701Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.701Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.702Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.702Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.702Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.702Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.702Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.703Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.703Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.704Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.704Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.705Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.705Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.705Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.705Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.706Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.706Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.706Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.706Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.706Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.707Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.707Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.707Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.709Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.713Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.713Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.714Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.714Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.715Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.716Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.716Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.717Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.717Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.717Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.718Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.718Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.718Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.719Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.719Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.720Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.721Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.721Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.722Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.722Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.722Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.723Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.730Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.730Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.730Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.731Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.731Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.731Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.731Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.732Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.732Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.732Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.732Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.732Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.733Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.733Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.733Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.733Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.734Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.734Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.734Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.734Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.734Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.735Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.735Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.735Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.736Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.736Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.736Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.736Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.736Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.737Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.737Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.737Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.737Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.737Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.738Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.738Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.738Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.738Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.744Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.745Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.745Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.745Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.745Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.746Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.746Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.746Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.746Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.746Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.747Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.747Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.747Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.747Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.747Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.748Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.748Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.748Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.748Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.748Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.749Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.749Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.749Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.749Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.749Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.750Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.750Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.750Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.750Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.750Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.751Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.751Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.751Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.751Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.751Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.752Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.752Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.752Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.752Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.752Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.753Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.753Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.753Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.753Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.753Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.754Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.754Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.754Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.760Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.760Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.760Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.761Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.763Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.763Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.764Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.764Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.764Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.765Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.765Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.766Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.766Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.766Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.767Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.767Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.767Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.768Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.768Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.768Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.769Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.769Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.770Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.770Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.770Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.776Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.777Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.777Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.777Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.778Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.778Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.778Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.779Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.779Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.779Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.780Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.780Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.780Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.780Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.780Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.781Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.781Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.781Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.781Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.781Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.782Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.782Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.782Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.782Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.782Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.783Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.783Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.783Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.783Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.783Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.784Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.784Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.784Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.784Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.784Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.785Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.785Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.785Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.785Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.785Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.786Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.786Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.786Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.790Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.791Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.792Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.792Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.793Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.793Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.794Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.794Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.794Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.794Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.795Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.795Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.795Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.795Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.796Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.796Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.796Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.796Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.796Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.797Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.797Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.797Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.797Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.797Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.798Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.798Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.798Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.798Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.798Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.799Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.799Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.799Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.799Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.799Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.800Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.800Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.800Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.800Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.800Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.801Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.801Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.801Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.805Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.805Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.806Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.806Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.807Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.807Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.810Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.810Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.811Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.811Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.811Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.812Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.813Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.813Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.814Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.814Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.814Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.815Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.815Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.816Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.816Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.816Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.822Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.822Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.823Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.823Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.823Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.823Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.823Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.824Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.824Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.825Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.825Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.826Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.826Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.827Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.827Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.827Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.828Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.828Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.828Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.828Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.829Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.829Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.829Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.830Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.830Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.830Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.830Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.830Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.831Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.831Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.831Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.831Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.831Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.832Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.832Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.832Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.832Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.832Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.833Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.837Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.837Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.838Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.838Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.838Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.838Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.838Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.839Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.839Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.839Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.839Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.839Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.840Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.840Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.841Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.841Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.842Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.842Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.843Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.843Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.844Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.844Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.844Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.845Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.845Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.845Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.845Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.845Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.846Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.846Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.846Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.846Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.846Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.847Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.847Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.847Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.847Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.847Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.848Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.848Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.848Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.848Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.848Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.849Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.853Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.853Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.853Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.854Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.854Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.854Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.856Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.856Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.857Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.857Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.858Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.858Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.859Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.860Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.860Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.861Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.861Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.862Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.863Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.863Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.864Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.864Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.864Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.865Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.870Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.870Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.870Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.870Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.871Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.871Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.871Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.871Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.871Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.872Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.872Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.872Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.872Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.872Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.873Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.873Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.873Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.873Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.873Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.874Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.874Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.874Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.875Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.876Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.876Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.876Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.877Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.877Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.878Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.878Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.878Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.878Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.879Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.879Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.879Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.879Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.880Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.880Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.880Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.880Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.880Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.881Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.885Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.885Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.886Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.886Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.886Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.886Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.886Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.887Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.887Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.887Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.887Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.887Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.888Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.888Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.888Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.888Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.888Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.889Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.889Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.889Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.889Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.889Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.890Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.890Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.890Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.890Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.891Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.891Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.892Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.893Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.893Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.894Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.894Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.894Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.894Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.895Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.895Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.895Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.895Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.895Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.896Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.896Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.896Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.896Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.896Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.897Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.901Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.901Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.903Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.903Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.904Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.904Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.905Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.905Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.905Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.906Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.906Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.906Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.907Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.907Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.908Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.908Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.909Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.910Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.911Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.911Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.912Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.912Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.912Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.918Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.919Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.919Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.919Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.919Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.919Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.920Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.920Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.920Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.920Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.920Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.921Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.921Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.921Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.921Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.921Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.922Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.922Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.922Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.922Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.922Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.923Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.923Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.923Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.923Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.924Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.924Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.925Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.926Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.926Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.927Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.928Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.932Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.932Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.932Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.933Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.933Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.933Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.933Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.933Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.934Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.934Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.934Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.934Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.935Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.935Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.935Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.935Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.935Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.936Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.936Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.936Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.936Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.937Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.937Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.937Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.937Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.937Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.938Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.938Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.938Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.938Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.938Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.939Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.939Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.939Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.939Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.939Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.940Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.940Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.940Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.940Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.940Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.941Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.942Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.942Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.943Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.947Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.947Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.948Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.948Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.949Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.950Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.951Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.951Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.951Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.952Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.952Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.952Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.953Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.953Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.953Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.954Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.954Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.954Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.955Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.955Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.955Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.956Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.956Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.957Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.957Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.958Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.959Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.964Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.965Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.965Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.965Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.965Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.966Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.966Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.966Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.966Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.966Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.967Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.967Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.967Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.967Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.967Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.968Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.968Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.968Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.968Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.968Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.969Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.969Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.969Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.969Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.969Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.970Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.970Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.970Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.970Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.970Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.971Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.971Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.971Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.971Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.971Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.972Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.972Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.972Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.972Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.972Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.973Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.973Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.973Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.973Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.973Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.974Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.974Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.975Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.980Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.980Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.980Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.980Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.980Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.981Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.981Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.981Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.981Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.981Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.982Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.982Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.982Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.982Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.983Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.983Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.983Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.983Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.983Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.984Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.984Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.984Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.984Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.984Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.985Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.985Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.985Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.985Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.986Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.986Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.986Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.986Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.986Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.987Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.987Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.987Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.987Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.987Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.988Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.988Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.988Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.988Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.988Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.988Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.989Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.989Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.989Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.989Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.989Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.990Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.990Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.990Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.990Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.991Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.996Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.996Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.999Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:48.999Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.000Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.000Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.000Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.001Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.001Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.001Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.002Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.002Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.002Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.002Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.003Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.003Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.003Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.004Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.004Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.005Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.005Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.005Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.006Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.006Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.006Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.006Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.007Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.015Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.015Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.016Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.016Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.016Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.016Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.017Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.017Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.017Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.017Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.017Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.018Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.018Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.018Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.018Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.018Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.019Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.019Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.019Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.019Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.019Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.020Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.020Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.020Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.020Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.020Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.021Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.021Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.021Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.021Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.021Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.021Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.022Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.022Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.023Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.023Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.023Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.023Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.023Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.024Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.024Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.025Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.025Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.026Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.026Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.026Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.027Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.027Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.028Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.028Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.028Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.028Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.028Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.029Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.029Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.029Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.029Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.029Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.029Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.030Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.030Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.030Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.030Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.030Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.031Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.031Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.031Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.031Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.032Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.032Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.032Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.032Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.032Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.033Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.033Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.033Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.033Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.033Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.034Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.034Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.034Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.034Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.035Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.035Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.035Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.035Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.036Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.036Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.036Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.036Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.037Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.037Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.037Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.037Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.043Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.043Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.044Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.045Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.047Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.048Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.049Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.049Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.050Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.050Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.050Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.051Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.051Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.051Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.052Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.052Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.052Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.052Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.053Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.053Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.061Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.061Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.062Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.062Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.062Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.062Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.063Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.063Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.064Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.064Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.065Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.066Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.066Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.066Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.066Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.067Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.067Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.067Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.067Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.067Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.067Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.068Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.068Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.068Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.068Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.068Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.069Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.069Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.073Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.074Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.074Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.074Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.075Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.076Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.076Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.077Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.077Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.078Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.078Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.078Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.078Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.079Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.079Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.079Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.080Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.081Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.081Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.081Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.081Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.081Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.082Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.082Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.082Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.082Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.082Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.082Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.083Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.083Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.083Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.083Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.083Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.084Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.084Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.084Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.084Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.084Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.085Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.085Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.085Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.089Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.089Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.090Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.090Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.091Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.094Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.094Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.094Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.095Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.096Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.096Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.097Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.098Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.098Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.098Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.099Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.099Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.100Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.100Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.100Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.101Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.101Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.106Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.106Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.106Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.106Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.107Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.107Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.107Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.108Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.108Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.108Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.109Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.109Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.110Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.110Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.111Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.111Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.112Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.113Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.113Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.113Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.114Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.114Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.114Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.114Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.115Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.115Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.115Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.115Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.115Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.115Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.116Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.116Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.116Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.116Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.116Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.117Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.121Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.121Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.122Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.122Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.122Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.122Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.122Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.123Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.123Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.123Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.123Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.123Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.123Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.124Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.124Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.125Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.125Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.126Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.126Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.126Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.127Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.127Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.128Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.128Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.128Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.128Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.129Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.129Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.129Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.129Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.129Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.129Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.130Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.130Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.130Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.130Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.130Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.131Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.131Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.131Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.131Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.131Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.132Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.132Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.132Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.132Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.132Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.133Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.137Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.137Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.137Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.138Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.138Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.140Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.141Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.142Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.142Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.143Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.144Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.145Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.145Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.145Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.146Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.146Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.147Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.147Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.148Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.148Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.149Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.154Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.154Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.155Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.155Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.155Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.155Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.155Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.156Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.156Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.156Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.156Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.156Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.157Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.157Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.157Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.158Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.158Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.158Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.159Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.160Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.161Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.161Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.161Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.162Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.162Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.162Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.163Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.163Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.163Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.163Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.168Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.168Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.169Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.169Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.169Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.169Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.170Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.170Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.170Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.170Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.170Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.171Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.171Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.171Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.171Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.171Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.171Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.172Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.172Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.172Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.172Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.172Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.173Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.173Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.173Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.173Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.173Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.174Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.174Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.174Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.175Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.175Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.176Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.176Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.177Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.177Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.177Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.178Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.178Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.178Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.178Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.178Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.179Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.179Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.179Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.179Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.179Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.180Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.184Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.184Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.184Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.185Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.185Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.186Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.188Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.188Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.188Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.188Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.189Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.189Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.189Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.190Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.190Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.191Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.191Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.192Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.192Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.193Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.193Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.194Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.194Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.202Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.202Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.202Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.202Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.202Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.203Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.203Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.203Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.203Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.204Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.204Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.204Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.204Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.204Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.204Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.205Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.205Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.205Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.205Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.205Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.206Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.206Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.206Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.206Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.206Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.207Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.207Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.207Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.207Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.208Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.208Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.209Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.209Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.209Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.210Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.211Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.215Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.215Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.215Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.215Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.215Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.216Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.216Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.216Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.216Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.216Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.216Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.217Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.217Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.217Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.217Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.217Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.218Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.218Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.218Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.218Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.218Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.219Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.219Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.219Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.219Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.219Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.220Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.220Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.220Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.220Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.220Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.221Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.221Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.221Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.221Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.221Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.222Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.222Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.222Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.222Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.222Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.222Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.223Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.223Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.223Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.223Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.223Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.224Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.224Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.225Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.225Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.226Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.226Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.231Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.231Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.232Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.232Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.233Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.233Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.235Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.235Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.235Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.236Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.236Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.236Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.237Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.237Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.237Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.237Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.238Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.238Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.238Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.239Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.239Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.239Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.239Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.240Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.244Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.252Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.253Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.253Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.253Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.254Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.254Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.254Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.254Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.255Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.255Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.255Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.255Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.255Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.256Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.256Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.256Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.256Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.256Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.257Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.257Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.258Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.258Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.259Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.259Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.259Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.260Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.260Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.261Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.261Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.261Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.261Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.261Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.262Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.262Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.262Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.262Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.262Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.263Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.263Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.263Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.263Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.263Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.264Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.264Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.264Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.264Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.264Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.265Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.265Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.265Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.265Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.265Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.265Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.266Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.266Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.266Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.266Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.266Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.267Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.267Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.267Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.267Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.267Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.268Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.268Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.268Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.268Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.268Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.269Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.269Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.269Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.269Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.269Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.270Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.270Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.270Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.270Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.270Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.270Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.271Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.271Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.271Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.271Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.271Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.272Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.272Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.272Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.278Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.278Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.279Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.279Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.279Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.282Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.282Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.282Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.283Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.283Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.283Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.284Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.284Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.284Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.285Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.285Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.285Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.285Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.286Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.286Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.286Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.287Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.287Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.288Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.288Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.295Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.295Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.296Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.296Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.296Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.297Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.297Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.297Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.297Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.298Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.298Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.298Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.298Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.298Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.299Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.299Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.299Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.299Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.299Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.300Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.300Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.300Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.300Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.300Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.300Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.301Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.301Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.301Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.301Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.301Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.302Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.302Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.302Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.302Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.302Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.303Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.303Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.303Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.303Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.303Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.304Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.304Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.304Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.304Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.310Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.310Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.311Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.311Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.311Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.311Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.311Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.313Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.313Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.314Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.314Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.314Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.314Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.314Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.315Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.315Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.315Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.315Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.315Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.316Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.316Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.316Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.316Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.316Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.316Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.317Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.317Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.317Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.317Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.317Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.318Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.318Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.318Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.318Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.318Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.319Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.319Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.319Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.319Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.319Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.324Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.324Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.325Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.325Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.326Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.327Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.330Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.330Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.330Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.331Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.331Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.331Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.331Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.332Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.332Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.332Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.333Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.333Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.333Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.334Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.334Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.334Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.334Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.335Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.336Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.346Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.347Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.349Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.349Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.350Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.351Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.352Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.355Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.356Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.357Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.358Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.359Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.360Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.360Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.360Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.361Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.362Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.362Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.362Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.363Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.363Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.363Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.364Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.364Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.364Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.364Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.365Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.365Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.365Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.365Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.365Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.366Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.366Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.366Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.366Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.366Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.367Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.367Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.367Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.367Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.367Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.368Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.372Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.372Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.372Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.373Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.373Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.374Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.377Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.377Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.378Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.379Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.379Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.379Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.379Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.380Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.380Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.380Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.381Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.381Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.381Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.382Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.382Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.382Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.382Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.383Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.383Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.384Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.388Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.388Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.389Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.389Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.389Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.389Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.389Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.390Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.390Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.390Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.390Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.391Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.391Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.392Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.392Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.393Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.393Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.393Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.394Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.394Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.394Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.395Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.395Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.395Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.396Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.396Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.397Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.397Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.397Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.397Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.398Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.398Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.398Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.398Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.399Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.403Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.403Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.404Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.404Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.404Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.404Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.405Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.405Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.405Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.405Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.405Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.406Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.406Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.406Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.406Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.406Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.407Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.407Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.408Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.409Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.409Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.410Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.410Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.411Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.412Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.412Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.412Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.413Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.413Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.413Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.414Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.414Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.419Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.419Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.419Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.419Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.420Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.423Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.423Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.424Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.424Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.425Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.425Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.426Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.427Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.428Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.428Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.429Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.429Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.429Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.430Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.430Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.436Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.436Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.436Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.436Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.437Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.437Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.438Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.438Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.438Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.439Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.439Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.439Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.439Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.440Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.440Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.440Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.441Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.442Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.442Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.443Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.443Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.444Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.444Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.444Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.444Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.445Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.445Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.445Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.446Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.446Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.451Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.451Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.451Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.451Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.451Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.452Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.452Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.452Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.452Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.452Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.453Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.453Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.454Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.454Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.455Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.455Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.455Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.455Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.456Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.456Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.456Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.456Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.456Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.456Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.457Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.457Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.458Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.458Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.459Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.459Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.460Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.460Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.460Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.461Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.461Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.461Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.461Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.462Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.462Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.462Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.462Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.467Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.467Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.467Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.468Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.468Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.469Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.471Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.471Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.471Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.472Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.472Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.472Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.473Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.473Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.473Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.474Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.474Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.476Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.476Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.477Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.484Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.484Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.484Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.484Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.484Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.485Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.485Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.485Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.485Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.486Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.486Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.486Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.486Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.486Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.486Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.487Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.487Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.487Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.487Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.487Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.488Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.488Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.488Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.488Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.488Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.489Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.489Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.489Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.489Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.489Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.490Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.490Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.490Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.490Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.491Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.491Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.492Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.492Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.493Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.493Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.498Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.498Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.499Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.499Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.499Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.499Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.499Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.500Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.500Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.500Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.500Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.501Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.501Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.501Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.501Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.501Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.502Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.502Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.502Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.502Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.502Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.503Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.503Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.503Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.503Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.504Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.504Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.504Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.504Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.504Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.505Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.505Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.505Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.505Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.505Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.505Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.506Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.506Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.506Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.506Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.506Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.507Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.507Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.507Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.508Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.508Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.509Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.514Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.514Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.515Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.515Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.516Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.516Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.519Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.519Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.519Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.519Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.520Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.520Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.520Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.521Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.521Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.521Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.521Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.522Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.522Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.522Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.523Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.523Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.523Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.524Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.524Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.531Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.531Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.532Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.532Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.532Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.532Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.532Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.533Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.533Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.533Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.533Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.534Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.534Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.534Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.534Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.534Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.535Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.535Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.535Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.535Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.535Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.536Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.536Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.536Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.536Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.536Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.536Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.537Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.537Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.537Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.537Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.537Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.538Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.538Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.538Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.538Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.538Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.539Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.539Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.539Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.539Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.539Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.539Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.540Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.540Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.540Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.540Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.541Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.546Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.546Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.546Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.546Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.547Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.547Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.547Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.547Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.547Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.548Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.548Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.548Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.548Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.548Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.549Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.549Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.549Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.549Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.549Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.550Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.550Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.550Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.550Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.550Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.550Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.551Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.551Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.551Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.551Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.552Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.552Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.552Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.552Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.552Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.553Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.553Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.553Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.553Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.553Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.554Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.554Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.554Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.554Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.554Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.555Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.555Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.555Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.555Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.555Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.556Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.556Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.556Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.556Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.556Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.556Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.557Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.563Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.563Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.563Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.566Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.566Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.566Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.567Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.567Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.568Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.569Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.569Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.570Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.570Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.570Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.570Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.571Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.571Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.572Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.572Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.572Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.572Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.573Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.580Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.580Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.580Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.581Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.581Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.582Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.582Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.582Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.582Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.582Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.583Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.583Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.583Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.583Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.583Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.584Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.584Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.584Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.584Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.584Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.584Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.585Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.585Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.585Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.585Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.585Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.586Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.586Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.586Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.586Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.586Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.587Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.587Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.587Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.587Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.587Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.587Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.588Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.588Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.588Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.588Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.588Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.589Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.594Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.594Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.594Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.595Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.595Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.595Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.595Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.595Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.596Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.596Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.596Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.596Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.596Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.597Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.597Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.597Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.597Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.597Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.597Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.598Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.598Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.598Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.598Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.598Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.599Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.599Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.599Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.599Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.599Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.600Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.600Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.600Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.600Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.600Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.601Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.601Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.601Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.601Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.601Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.601Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.602Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.602Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.602Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.602Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.602Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.603Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.603Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.603Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.603Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.603Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.604Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.604Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.604Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.604Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.604Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.610Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.610Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.610Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.611Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.611Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.612Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.615Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.615Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.615Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.616Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.616Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.616Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.617Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.617Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.617Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.617Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.618Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.618Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.618Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.619Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.619Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.619Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.619Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.620Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.627Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.628Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.628Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.628Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.628Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.629Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.629Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.629Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.629Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.629Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.630Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.630Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.630Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.630Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.630Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.630Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.631Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.631Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.631Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.631Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.631Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.631Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.631Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.631Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.632Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.632Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.632Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.632Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.632Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.632Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.632Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.632Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.633Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.633Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.633Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.633Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.633Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.633Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.633Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.633Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.634Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.634Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.634Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.634Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.634Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.634Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.634Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.634Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.635Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.635Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.635Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.635Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.635Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.635Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.639Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.640Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.641Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.642Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.644Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.644Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.645Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.645Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.645Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.645Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.646Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.646Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.646Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.646Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.646Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.647Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.647Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.647Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.648Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.648Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.648Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.649Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.649Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.649Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.649Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.649Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.650Z] ⚠️  Error recovering notification: SQLITE_ERROR: table notifications has no column named user_id
[2025-08-05T23:19:49.650Z] ✅ Notifications recovery complete: 0/3056 recovered
[2025-08-05T23:19:49.655Z] ✅ Verification - streams: 16 records
[2025-08-05T23:19:49.656Z] ✅ Verification - videos: 28 records
[2025-08-05T23:19:49.656Z] ✅ Verification - users: 93 records
[2025-08-05T23:19:49.656Z] ✅ Verification - notifications: 28 records
[2025-08-05T23:19:49.656Z] ✅ Verification - subscription_plans: 5 records