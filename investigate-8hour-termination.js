#!/usr/bin/env node

/**
 * Investigate 8-Hour Termination
 * Deep investigation into what caused the 8-hour stream termination
 */

const { supabase } = require('./db/database');

console.log('🔍 Investigating 8-Hour Stream Termination');
console.log('==========================================\n');

async function findMusicCowStream() {
  try {
    const { data, error } = await supabase
      .from('streams')
      .select('id, title, status, start_time, duration, created_at, updated_at')
      .or('title.ilike.%MusicCow%,title.ilike.%musiccow%')
      .order('updated_at', { ascending: false })
      .limit(5);

    if (error) {
      throw new Error(`Failed to fetch MusicCow streams: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    throw new Error(`Database query failed: ${error.message}`);
  }
}

async function checkAllStreamsWithDuration() {
  try {
    const { data, error } = await supabase
      .from('streams')
      .select('id, title, status, start_time, duration, created_at, updated_at')
      .not('duration', 'is', null)
      .gt('duration', 0)
      .order('updated_at', { ascending: false })
      .limit(10);

    if (error) {
      throw new Error(`Failed to fetch streams with duration: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    throw new Error(`Database query failed: ${error.message}`);
  }
}

async function checkRecentLongStreams() {
  try {
    // Get streams from the last day with start_time
    const { data, error } = await supabase
      .from('streams')
      .select('id, title, status, start_time, duration, created_at, updated_at')
      .not('start_time', 'is', null)
      .gte('updated_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

    if (error) {
      throw new Error(`Failed to fetch recent streams: ${error.message}`);
    }

    // Calculate runtime and filter for long streams (>5 hours)
    const longStreams = (data || [])
      .map(stream => {
        const startTime = new Date(stream.start_time);
        const endTime = new Date(stream.updated_at);
        const runtimeMinutes = Math.floor((endTime - startTime) / (1000 * 60));

        return {
          ...stream,
          runtime_minutes: runtimeMinutes
        };
      })
      .filter(stream => stream.runtime_minutes > 300) // More than 5 hours
      .sort((a, b) => b.runtime_minutes - a.runtime_minutes)
      .slice(0, 10);

    return longStreams;
  } catch (error) {
    throw new Error(`Database query failed: ${error.message}`);
  }
}

async function main() {
  try {
    console.log('1️⃣ SEARCHING FOR MUSICCOW STREAM:');
    console.log('=================================');
    
    const musicCowStreams = await findMusicCowStream();
    
    if (musicCowStreams.length === 0) {
      console.log('❌ No MusicCow streams found in database');
    } else {
      musicCowStreams.forEach((stream, index) => {
        const startTime = stream.start_time ? new Date(stream.start_time) : null;
        const updatedTime = new Date(stream.updated_at);
        const runningTime = startTime && stream.updated_at ? 
          Math.floor((updatedTime - startTime) / 1000 / 60) : 0;
        
        console.log(`${index + 1}. "${stream.title}" (${stream.id})`);
        console.log(`   Status: ${stream.status}`);
        console.log(`   Duration Limit: ${stream.duration || 'None'} minutes`);
        if (startTime) {
          console.log(`   Started: ${startTime.toLocaleString()}`);
          console.log(`   Last Updated: ${updatedTime.toLocaleString()}`);
          console.log(`   Total Runtime: ${runningTime} minutes (${Math.floor(runningTime/60)}h ${runningTime%60}m)`);
          
          if (runningTime >= 470 && runningTime <= 490) {
            console.log(`   🚨 MATCH: This is likely the 8-hour terminated stream!`);
            
            if (stream.duration && stream.duration > 0) {
              console.log(`   🎯 CAUSE: Stream had duration limit of ${stream.duration} minutes`);
            } else {
              console.log(`   ❓ MYSTERY: No duration limit set, termination cause unknown`);
            }
          }
        }
        console.log('');
      });
    }
    
    console.log('\n2️⃣ CHECKING ALL STREAMS WITH DURATION LIMITS:');
    console.log('==============================================');
    
    const streamsWithDuration = await checkAllStreamsWithDuration();
    
    if (streamsWithDuration.length === 0) {
      console.log('✅ No streams found with duration limits');
    } else {
      console.log(`🚨 CRITICAL: Found ${streamsWithDuration.length} streams with duration limits!`);
      console.log('These streams will auto-terminate when their duration is reached:\n');
      
      streamsWithDuration.forEach((stream, index) => {
        console.log(`${index + 1}. "${stream.title}" (${stream.id})`);
        console.log(`   Duration Limit: ${stream.duration} minutes (${Math.floor(stream.duration/60)}h ${stream.duration%60}m)`);
        console.log(`   Status: ${stream.status}`);
        console.log(`   Created: ${new Date(stream.created_at).toLocaleString()}`);
        
        if (stream.duration >= 470 && stream.duration <= 490) {
          console.log(`   🎯 LIKELY CAUSE: This ~8 hour limit caused the MusicCow termination!`);
        }
        console.log('');
      });
    }
    
    console.log('\n3️⃣ CHECKING RECENT LONG-RUNNING STREAMS:');
    console.log('========================================');
    
    const longStreams = await checkRecentLongStreams();
    
    if (longStreams.length === 0) {
      console.log('❌ No long-running streams found in recent history');
    } else {
      console.log(`📊 Found ${longStreams.length} streams that ran for 5+ hours:\n`);
      
      longStreams.forEach((stream, index) => {
        const runtime = stream.runtime_minutes || 0;
        console.log(`${index + 1}. "${stream.title}" (${stream.id})`);
        console.log(`   Runtime: ${runtime} minutes (${Math.floor(runtime/60)}h ${runtime%60}m)`);
        console.log(`   Duration Limit: ${stream.duration || 'None'} minutes`);
        console.log(`   Status: ${stream.status}`);
        console.log(`   Started: ${stream.start_time ? new Date(stream.start_time).toLocaleString() : 'Unknown'}`);
        console.log(`   Ended: ${new Date(stream.updated_at).toLocaleString()}`);
        
        if (runtime >= 470 && runtime <= 490) {
          console.log(`   🎯 MATCH: This is likely the 8-hour terminated stream!`);
        }
        
        if (runtime >= 1440) { // 24+ hours
          console.log(`   🎉 SUCCESS: This stream ran for 24+ hours!`);
        }
        console.log('');
      });
    }
    
    console.log('\n📋 ANALYSIS SUMMARY:');
    console.log('====================');
    
    if (streamsWithDuration.length > 0) {
      console.log('🚨 CRITICAL ISSUE FOUND:');
      console.log(`   - ${streamsWithDuration.length} streams still have duration limits`);
      console.log('   - These will auto-terminate when duration is reached');
      console.log('   - This prevents true 24/7 streaming capability');
      console.log('\n🔧 REQUIRED ACTION:');
      console.log('   - Remove ALL duration limits from streams');
      console.log('   - Run: node fix-stream-durations.js --fix');
    } else {
      console.log('✅ No duration limits found - 24/7 streaming should be possible');
    }
    
    const maxRuntime = Math.max(...longStreams.map(s => s.runtime_minutes || 0));
    if (maxRuntime >= 1440) {
      console.log(`\n🎉 24-HOUR CAPABILITY CONFIRMED:`);
      console.log(`   - Longest stream: ${Math.floor(maxRuntime/60)} hours ${maxRuntime%60} minutes`);
      console.log(`   - True 24/7 streaming is working!`);
    } else if (maxRuntime >= 480) {
      console.log(`\n⚠️  PARTIAL SUCCESS:`);
      console.log(`   - Longest stream: ${Math.floor(maxRuntime/60)} hours ${maxRuntime%60} minutes`);
      console.log(`   - Need to test 24+ hour capability`);
    } else {
      console.log(`\n❌ 24-HOUR CAPABILITY NOT VERIFIED:`);
      console.log(`   - Longest stream: ${Math.floor(maxRuntime/60)} hours ${maxRuntime%60} minutes`);
      console.log(`   - Need to test longer streaming durations`);
    }
    
  } catch (error) {
    console.error('❌ Investigation failed:', error.message);
    process.exit(1);
  }
}

main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
