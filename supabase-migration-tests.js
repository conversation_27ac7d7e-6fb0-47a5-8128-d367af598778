#!/usr/bin/env node

/**
 * Supabase Migration Testing Suite for StreamOnPod
 * 
 * This test suite verifies that the Supabase migration was successful
 * by testing all critical functionality.
 */

const supabaseDb = require('./supabase-database-adapter');

class SupabaseMigrationTests {
  constructor() {
    this.testResults = [];
    this.passedTests = 0;
    this.failedTests = 0;
  }

  async runAllTests() {
    console.log('🧪 StreamOnPod Supabase Migration Test Suite\n');
    
    try {
      // Connection tests
      await this.testDatabaseConnection();
      
      // Data integrity tests
      await this.testDataIntegrity();
      
      // CRUD operation tests
      await this.testUserOperations();
      await this.testStreamOperations();
      await this.testVideoOperations();
      await this.testSubscriptionOperations();
      await this.testTransactionOperations();
      
      // Complex query tests
      await this.testComplexQueries();
      
      // Performance tests
      await this.testPerformance();
      
      this.printResults();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
    }
  }

  async testDatabaseConnection() {
    console.log('🔌 Testing Database Connection...');
    
    try {
      const health = await supabaseDb.healthCheck();
      this.addTestResult('Database Connection', health.healthy, health.error);
      
      if (health.healthy) {
        console.log('✅ Database connection successful');
      } else {
        console.log('❌ Database connection failed:', health.error);
      }
    } catch (error) {
      this.addTestResult('Database Connection', false, error.message);
    }
  }

  async testDataIntegrity() {
    console.log('\n📊 Testing Data Integrity...');
    
    const expectedCounts = {
      users: 93,
      streams: 7,
      videos: 28,
      subscription_plans: 5,
      user_subscriptions: 113,
      transactions: 88,
      notifications: 37,
      role_permissions: 117
    };

    for (const [table, expectedCount] of Object.entries(expectedCounts)) {
      try {
        const { data, error } = await supabaseDb.supabase
          .from(table)
          .select('count');

        if (error) throw error;

        const actualCount = data.length;
        const passed = actualCount === expectedCount;
        
        this.addTestResult(
          `Data Integrity - ${table}`, 
          passed, 
          passed ? null : `Expected ${expectedCount}, got ${actualCount}`
        );

        if (passed) {
          console.log(`✅ ${table}: ${actualCount} records (correct)`);
        } else {
          console.log(`❌ ${table}: ${actualCount} records (expected ${expectedCount})`);
        }
      } catch (error) {
        this.addTestResult(`Data Integrity - ${table}`, false, error.message);
        console.log(`❌ ${table}: Error - ${error.message}`);
      }
    }
  }

  async testUserOperations() {
    console.log('\n👥 Testing User Operations...');
    
    try {
      // Test getting existing user
      const existingUser = await supabaseDb.getUserByUsername('aufanirsad');
      this.addTestResult('Get User by Username', !!existingUser, existingUser ? null : 'User not found');
      
      if (existingUser) {
        console.log('✅ Get user by username successful');
        
        // Test getting user by ID
        const userById = await supabaseDb.getUserById(existingUser.id);
        this.addTestResult('Get User by ID', !!userById, userById ? null : 'User not found');
        console.log(userById ? '✅ Get user by ID successful' : '❌ Get user by ID failed');
        
        // Test user update (non-destructive)
        const originalEmail = existingUser.email;
        const updateResult = await supabaseDb.updateUser(existingUser.id, {
          updated_at: new Date().toISOString()
        });
        this.addTestResult('Update User', !!updateResult, updateResult ? null : 'Update failed');
        console.log(updateResult ? '✅ Update user successful' : '❌ Update user failed');
      }
      
    } catch (error) {
      this.addTestResult('User Operations', false, error.message);
      console.log('❌ User operations failed:', error.message);
    }
  }

  async testStreamOperations() {
    console.log('\n📺 Testing Stream Operations...');
    
    try {
      // Get streams for a user
      const { data: users } = await supabaseDb.supabase
        .from('users')
        .select('id')
        .limit(1);

      if (users && users.length > 0) {
        const userId = users[0].id;
        const userStreams = await supabaseDb.getStreamsByUserId(userId);
        
        this.addTestResult('Get Streams by User', Array.isArray(userStreams), 
          Array.isArray(userStreams) ? null : 'Invalid response format');
        
        console.log(`✅ Get streams by user successful (${userStreams.length} streams)`);
        
        // Test getting a specific stream if available
        if (userStreams.length > 0) {
          const stream = await supabaseDb.getStreamById(userStreams[0].id);
          this.addTestResult('Get Stream by ID', !!stream, stream ? null : 'Stream not found');
          console.log(stream ? '✅ Get stream by ID successful' : '❌ Get stream by ID failed');
        }
      }
      
    } catch (error) {
      this.addTestResult('Stream Operations', false, error.message);
      console.log('❌ Stream operations failed:', error.message);
    }
  }

  async testVideoOperations() {
    console.log('\n🎥 Testing Video Operations...');
    
    try {
      // Get videos for a user
      const { data: users } = await supabaseDb.supabase
        .from('users')
        .select('id')
        .limit(1);

      if (users && users.length > 0) {
        const userId = users[0].id;
        const userVideos = await supabaseDb.getVideosByUserId(userId);
        
        this.addTestResult('Get Videos by User', Array.isArray(userVideos), 
          Array.isArray(userVideos) ? null : 'Invalid response format');
        
        console.log(`✅ Get videos by user successful (${userVideos.length} videos)`);
        
        // Test getting a specific video if available
        if (userVideos.length > 0) {
          const video = await supabaseDb.getVideoById(userVideos[0].id);
          this.addTestResult('Get Video by ID', !!video, video ? null : 'Video not found');
          console.log(video ? '✅ Get video by ID successful' : '❌ Get video by ID failed');
        }
      }
      
    } catch (error) {
      this.addTestResult('Video Operations', false, error.message);
      console.log('❌ Video operations failed:', error.message);
    }
  }

  async testSubscriptionOperations() {
    console.log('\n💳 Testing Subscription Operations...');
    
    try {
      // Get subscription plans
      const plans = await supabaseDb.getSubscriptionPlans();
      this.addTestResult('Get Subscription Plans', Array.isArray(plans) && plans.length > 0, 
        Array.isArray(plans) ? null : 'No plans found');
      
      console.log(`✅ Get subscription plans successful (${plans.length} plans)`);
      
      // Test getting user subscription
      const { data: users } = await supabaseDb.supabase
        .from('user_subscriptions')
        .select('user_id')
        .limit(1);

      if (users && users.length > 0) {
        const userId = users[0].user_id;
        const subscription = await supabaseDb.getUserSubscription(userId);
        
        this.addTestResult('Get User Subscription', !!subscription, 
          subscription ? null : 'No subscription found');
        
        console.log(subscription ? '✅ Get user subscription successful' : '⚠️  No active subscription found (normal)');
      }
      
    } catch (error) {
      this.addTestResult('Subscription Operations', false, error.message);
      console.log('❌ Subscription operations failed:', error.message);
    }
  }

  async testTransactionOperations() {
    console.log('\n💰 Testing Transaction Operations...');
    
    try {
      // Get transactions for a user
      const { data: transactions } = await supabaseDb.supabase
        .from('transactions')
        .select('user_id')
        .limit(1);

      if (transactions && transactions.length > 0) {
        const userId = transactions[0].user_id;
        const userTransactions = await supabaseDb.getTransactionsByUserId(userId);
        
        this.addTestResult('Get Transactions by User', Array.isArray(userTransactions), 
          Array.isArray(userTransactions) ? null : 'Invalid response format');
        
        console.log(`✅ Get transactions by user successful (${userTransactions.length} transactions)`);
      } else {
        console.log('⚠️  No transactions found to test (normal for new installations)');
        this.addTestResult('Get Transactions by User', true, null);
      }
      
    } catch (error) {
      this.addTestResult('Transaction Operations', false, error.message);
      console.log('❌ Transaction operations failed:', error.message);
    }
  }

  async testComplexQueries() {
    console.log('\n🔍 Testing Complex Queries...');
    
    try {
      // Test search functionality
      const { data: searchResults, error } = await supabaseDb.supabase
        .from('streams')
        .select('*')
        .ilike('title', '%live%')
        .limit(5);

      if (error) throw error;

      this.addTestResult('Search Streams', Array.isArray(searchResults), 
        Array.isArray(searchResults) ? null : 'Search failed');
      
      console.log(`✅ Search streams successful (${searchResults.length} results)`);
      
      // Test join-like query
      const { data: streamsWithUsers, error: joinError } = await supabaseDb.supabase
        .from('streams')
        .select(`
          *,
          users (username, email)
        `)
        .limit(5);

      if (joinError) throw joinError;

      this.addTestResult('Join Query (Streams with Users)', Array.isArray(streamsWithUsers), 
        Array.isArray(streamsWithUsers) ? null : 'Join query failed');
      
      console.log(`✅ Join query successful (${streamsWithUsers.length} results)`);
      
    } catch (error) {
      this.addTestResult('Complex Queries', false, error.message);
      console.log('❌ Complex queries failed:', error.message);
    }
  }

  async testPerformance() {
    console.log('\n⚡ Testing Performance...');
    
    try {
      // Test query performance
      const startTime = Date.now();
      
      const { data, error } = await supabaseDb.supabase
        .from('users')
        .select('*')
        .limit(50);

      const endTime = Date.now();
      const queryTime = endTime - startTime;

      if (error) throw error;

      const performanceGood = queryTime < 1000; // Should be under 1 second
      this.addTestResult('Query Performance', performanceGood, 
        performanceGood ? null : `Query took ${queryTime}ms (too slow)`);
      
      console.log(`✅ Query performance: ${queryTime}ms (${performanceGood ? 'good' : 'slow'})`);
      
    } catch (error) {
      this.addTestResult('Performance Test', false, error.message);
      console.log('❌ Performance test failed:', error.message);
    }
  }

  addTestResult(testName, passed, error) {
    this.testResults.push({
      name: testName,
      passed,
      error
    });
    
    if (passed) {
      this.passedTests++;
    } else {
      this.failedTests++;
    }
  }

  printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 MIGRATION TEST RESULTS');
    console.log('='.repeat(60));
    
    console.log(`\n📊 SUMMARY:`);
    console.log(`   Total tests: ${this.testResults.length}`);
    console.log(`   Passed: ${this.passedTests} ✅`);
    console.log(`   Failed: ${this.failedTests} ❌`);
    console.log(`   Success rate: ${Math.round((this.passedTests / this.testResults.length) * 100)}%`);
    
    if (this.failedTests > 0) {
      console.log(`\n❌ FAILED TESTS:`);
      this.testResults
        .filter(test => !test.passed)
        .forEach(test => {
          console.log(`   • ${test.name}: ${test.error}`);
        });
    }
    
    console.log(`\n${this.failedTests === 0 ? '✅' : '⚠️'} MIGRATION STATUS: ${this.failedTests === 0 ? 'SUCCESS' : 'NEEDS ATTENTION'}`);
    
    if (this.failedTests === 0) {
      console.log('\n🎉 All tests passed! Your Supabase migration is successful.');
      console.log('✅ You can now safely switch to using Supabase in production.');
    } else {
      console.log('\n⚠️  Some tests failed. Please review and fix issues before production deployment.');
      console.log('🔄 You may need to adjust your migration or code implementation.');
    }
    
    console.log('\n' + '='.repeat(60));
  }
}

// Run tests if called directly
if (require.main === module) {
  const tests = new SupabaseMigrationTests();
  tests.runAllTests().catch(console.error);
}

module.exports = SupabaseMigrationTests;
