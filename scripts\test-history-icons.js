#!/usr/bin/env node

/**
 * Test script to verify platform icons in stream history
 * This script checks the platform_icon values in the database and tests the icon display logic
 */

const path = require('path');

// Add the project root to the module path
const projectRoot = path.resolve(__dirname, '..');
process.chdir(projectRoot);

// Import required modules
const { db } = require('../db/database');

console.log('🔧 Testing Stream History Platform Icons');
console.log('=======================================\n');

async function checkHistoryIcons() {
  console.log('1. Checking platform_icon values in stream_history table...');
  
  try {
    const { data: historyEntries, error } = await db.supabase
      .from('stream_history')
      .select('id, title, platform, platform_icon')
      .order('start_time', { ascending: false })
      .limit(10);

    if (error) {
      throw new Error(`Failed to get history entries: ${error.message}`);
    }

    console.log(`   📊 Found ${historyEntries?.length || 0} history entries`);

    if (!historyEntries || historyEntries.length === 0) {
      console.log('   ℹ️  No history entries found. Create some streams to test.');
      return;
    }
    
    console.log('\n   Platform icon values:');
    historyEntries.forEach((entry, index) => {
      console.log(`   ${index + 1}. "${entry.title}"`);
      console.log(`      Platform: ${entry.platform || 'null'}`);
      console.log(`      Platform Icon: ${entry.platform_icon || 'null'}`);
      console.log('');
    });
    
  } catch (error) {
    console.error(`   ❌ Error checking history icons: ${error.message}`);
  }
}

async function testIconMapping() {
  console.log('2. Testing platform icon mapping logic...');
  
  const platforms = ['YouTube', 'Facebook', 'Twitch', 'TikTok', 'Instagram', 'Shopee Live', 'Restream.io', 'Custom'];
  
  // Import the helper function from app.js
  const helpers = {
    getPlatformIcon: function (platform) {
      switch (platform) {
        case 'YouTube': return 'brand-youtube';
        case 'Facebook': return 'brand-facebook';
        case 'Twitch': return 'brand-twitch';
        case 'TikTok': return 'brand-tiktok';
        case 'Instagram': return 'brand-instagram';
        case 'Shopee Live': return 'shopping-bag';
        case 'Restream.io': return 'live-photo';
        default: return 'broadcast';
      }
    },
    getPlatformColor: function (platform) {
      switch (platform) {
        case 'YouTube': return 'red-500';
        case 'Facebook': return 'blue-500';
        case 'Twitch': return 'purple-500';
        case 'TikTok': return 'gray-100';
        case 'Instagram': return 'pink-500';
        case 'Shopee Live': return 'orange-500';
        case 'Restream.io': return 'teal-500';
        default: return 'gray-400';
      }
    }
  };
  
  console.log('   Platform mappings:');
  platforms.forEach(platform => {
    const icon = helpers.getPlatformIcon(platform);
    const color = helpers.getPlatformColor(platform);
    const fullIconClass = `ti ti-${icon}`;
    console.log(`   ${platform.padEnd(15)} → ${fullIconClass.padEnd(20)} (${color})`);
  });
}

async function checkStreamsTable() {
  console.log('\n3. Checking platform_icon values in streams table...');
  
  try {
    const streams = await new Promise((resolve, reject) => {
      db.all(
        'SELECT id, title, platform, platform_icon FROM streams ORDER BY created_at DESC LIMIT 5',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
    
    console.log(`   📊 Found ${streams.length} stream entries`);
    
    if (streams.length === 0) {
      console.log('   ℹ️  No stream entries found.');
      return;
    }
    
    console.log('\n   Stream platform icon values:');
    streams.forEach((stream, index) => {
      console.log(`   ${index + 1}. "${stream.title}"`);
      console.log(`      Platform: ${stream.platform || 'null'}`);
      console.log(`      Platform Icon: ${stream.platform_icon || 'null'}`);
      console.log('');
    });
    
  } catch (error) {
    console.error(`   ❌ Error checking streams: ${error.message}`);
  }
}

async function generateTestHTML() {
  console.log('4. Generating test HTML for icon display...');
  
  try {
    const historyEntries = await new Promise((resolve, reject) => {
      db.all(
        'SELECT id, title, platform, platform_icon FROM stream_history ORDER BY start_time DESC LIMIT 3',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
    
    if (historyEntries.length === 0) {
      console.log('   ℹ️  No history entries to generate HTML for.');
      return;
    }
    
    console.log('   Generated HTML (for testing):');
    console.log('   ```html');
    
    historyEntries.forEach(entry => {
      const platform = entry.platform || 'Custom';
      const iconClass = getPlatformIcon(platform);
      const colorClass = getPlatformColor(platform);
      
      console.log(`   <!-- ${entry.title} -->`);
      console.log(`   <i class="ti ti-${iconClass} text-${colorClass} mr-1.5"></i>`);
      console.log(`   <span>${platform}</span>`);
      console.log('');
    });
    
    console.log('   ```');
    
  } catch (error) {
    console.error(`   ❌ Error generating test HTML: ${error.message}`);
  }
}

function getPlatformIcon(platform) {
  switch (platform) {
    case 'YouTube': return 'brand-youtube';
    case 'Facebook': return 'brand-facebook';
    case 'Twitch': return 'brand-twitch';
    case 'TikTok': return 'brand-tiktok';
    case 'Instagram': return 'brand-instagram';
    case 'Shopee Live': return 'shopping-bag';
    case 'Restream.io': return 'live-photo';
    default: return 'broadcast';
  }
}

function getPlatformColor(platform) {
  switch (platform) {
    case 'YouTube': return 'red-500';
    case 'Facebook': return 'blue-500';
    case 'Twitch': return 'purple-500';
    case 'TikTok': return 'gray-100';
    case 'Instagram': return 'pink-500';
    case 'Shopee Live': return 'orange-500';
    case 'Restream.io': return 'teal-500';
    default: return 'gray-400';
  }
}

async function runTests() {
  try {
    await checkHistoryIcons();
    await testIconMapping();
    await checkStreamsTable();
    await generateTestHTML();
    
    console.log('\n🎉 Platform icon tests completed!');
    console.log('\nFix Summary:');
    console.log('- Updated history.ejs to use helpers.getPlatformIcon() instead of entry.platform_icon');
    console.log('- This ensures consistent icon mapping based on platform name');
    console.log('- Icons will now display correctly in the stream history table');
    
  } catch (error) {
    console.error(`\n❌ Test failed: ${error.message}`);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = {
  checkHistoryIcons,
  testIconMapping,
  checkStreamsTable,
  generateTestHTML,
  runTests
};
