#!/usr/bin/env node

/**
 * Check Database Status and Recent Streams
 * Simple script to check database integrity and recent stream activity
 */

const { db } = require('./db/database');

console.log('🔍 Database Status Check');
console.log('========================\n');

async function checkRecentStreams() {
  try {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

    const { data, error } = await db.supabase
      .from('streams')
      .select('id, title, status, start_time, duration, created_at, updated_at')
      .gt('updated_at', twentyFourHoursAgo)
      .order('updated_at', { ascending: false })
      .limit(10);

    if (error) {
      throw new Error(`Failed to get recent streams: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    console.error('Error checking recent streams:', error.message);
    throw error;
  }
}

async function checkStreamCount() {
  return new Promise((resolve, reject) => {
    db.get(`
      SELECT COUNT(*) as total_streams
      FROM streams
    `, [], (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

async function checkLiveStreams() {
  return new Promise((resolve, reject) => {
    db.all(`
      SELECT id, title, status, start_time, duration, created_at, updated_at
      FROM streams 
      WHERE status = 'live'
      ORDER BY updated_at DESC
    `, [], (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows || []);
      }
    });
  });
}

async function main() {
  try {
    console.log('📊 TOTAL STREAMS:');
    console.log('=================');
    const streamCount = await checkStreamCount();
    console.log(`Total streams in database: ${streamCount.total_streams}\n`);

    console.log('🔴 LIVE STREAMS:');
    console.log('================');
    const liveStreams = await checkLiveStreams();
    if (liveStreams.length === 0) {
      console.log('❌ No live streams found\n');
    } else {
      liveStreams.forEach((stream, index) => {
        console.log(`${index + 1}. ${stream.title}`);
        console.log(`   ID: ${stream.id}`);
        console.log(`   Status: ${stream.status}`);
        console.log(`   Start Time: ${stream.start_time}`);
        console.log(`   Duration: ${stream.duration}`);
        console.log(`   Updated: ${stream.updated_at}`);
        console.log('');
      });
    }

    console.log('📅 RECENT STREAMS (Last 24 hours):');
    console.log('===================================');
    const recentStreams = await checkRecentStreams();
    if (recentStreams.length === 0) {
      console.log('❌ No recent streams found\n');
    } else {
      recentStreams.forEach((stream, index) => {
        console.log(`${index + 1}. ${stream.title}`);
        console.log(`   ID: ${stream.id}`);
        console.log(`   Status: ${stream.status}`);
        console.log(`   Start Time: ${stream.start_time}`);
        console.log(`   Duration: ${stream.duration}`);
        console.log(`   Created: ${stream.created_at}`);
        console.log(`   Updated: ${stream.updated_at}`);
        console.log('');
      });
    }

  } catch (error) {
    console.error('❌ Database error:', error.message);
    
    if (error.message.includes('SQLITE_CORRUPT')) {
      console.log('\n🚨 DATABASE CORRUPTION DETECTED!');
      console.log('Possible solutions:');
      console.log('1. Restore from backup');
      console.log('2. Run database repair');
      console.log('3. Check disk space and file permissions');
    }
    
    process.exit(1);
  }
}

main().then(() => {
  console.log('✅ Database check completed');
  process.exit(0);
}).catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
