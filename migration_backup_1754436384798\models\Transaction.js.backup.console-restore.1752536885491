const { db } = require('../db/database');
const { v4: uuidv4 } = require('uuid');

class Transaction {
  /**
   * Create a new transaction
   * @param {Object} transactionData - Transaction data
   * @returns {Promise<Object>} Created transaction
   */
  static async create(transactionData) {
    return new Promise((resolve, reject) => {
      const {
        user_id,
        plan_id,
        order_id,
        amount_idr,
        payment_method = 'midtrans',
        status = 'pending',
        midtrans_token = null,
        midtrans_redirect_url = null
      } = transactionData;

      const id = uuidv4();
      const created_at = new Date().toISOString();

      const sql = `
        INSERT INTO transactions (
          id, user_id, plan_id, order_id, amount_idr,
          payment_method, status, midtrans_token, midtrans_redirect_url, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      db.run(sql, [
        id, user_id, plan_id, order_id, amount_idr,
        payment_method, status, midtrans_token, midtrans_redirect_url, created_at
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({
            id,
            user_id,
            plan_id,
            order_id,
            amount_idr,
            payment_method,
            status,
            midtrans_token,
            midtrans_redirect_url,
            created_at
          });
        }
      });
    });
  }

  /**
   * Find transaction by order ID
   * @param {string} orderId - Order ID
   * @returns {Promise<Object|null>} Transaction data
   */
  static async findByOrderId(orderId) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT t.*, sp.name as plan_name, u.username, u.email
        FROM transactions t
        LEFT JOIN subscription_plans sp ON t.plan_id = sp.id
        LEFT JOIN users u ON t.user_id = u.id
        WHERE t.order_id = ?
      `;

      db.get(sql, [orderId], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row || null);
        }
      });
    });
  }

  /**
   * Update transaction status
   * @param {string} orderId - Order ID
   * @param {string} status - New status
   * @param {Object} additionalData - Additional data to update
   * @returns {Promise<boolean>} Success status
   */
  static async updateStatus(orderId, status, additionalData = {}) {
    return new Promise((resolve, reject) => {
      const updates = ['status = ?'];
      const values = [status];

      // Add additional fields to update
      Object.keys(additionalData).forEach(key => {
        updates.push(`${key} = ?`);
        values.push(additionalData[key]);
      });

      values.push(new Date().toISOString()); // updated_at
      values.push(orderId); // WHERE condition

      const sql = `
        UPDATE transactions 
        SET ${updates.join(', ')}, updated_at = ?
        WHERE order_id = ?
      `;

      db.run(sql, values, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  /**
   * Get user transactions
   * @param {string} userId - User ID
   * @param {number} limit - Limit results
   * @returns {Promise<Array>} User transactions
   */
  static async getUserTransactions(userId, limit = 10) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT t.*, sp.name as plan_name
        FROM transactions t
        LEFT JOIN subscription_plans sp ON t.plan_id = sp.id
        WHERE t.user_id = ?
        ORDER BY t.created_at DESC
        LIMIT ?
      `;

      db.all(sql, [userId, limit], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  /**
   * Get all transactions (admin)
   * @param {number} limit - Limit results
   * @param {number} offset - Offset for pagination
   * @returns {Promise<Array>} All transactions
   */
  static async getAllTransactions(limit = 50, offset = 0) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT t.*, sp.name as plan_name, u.username, u.email
        FROM transactions t
        LEFT JOIN subscription_plans sp ON t.plan_id = sp.id
        LEFT JOIN users u ON t.user_id = u.id
        ORDER BY t.created_at DESC
        LIMIT ? OFFSET ?
      `;

      db.all(sql, [limit, offset], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  /**
   * Generate unique order ID
   * @param {string} userId - User ID
   * @param {string} planId - Plan ID
   * @param {string} type - Order type ('subscription' or 'renewal')
   * @returns {string} Unique order ID
   */
  static generateOrderId(userId, planId, type = 'subscription') {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const prefix = type === 'renewal' ? 'RNW' : 'SOP';
    return `${prefix}-${userId.substring(0, 8)}-${planId.substring(0, 8)}-${timestamp}-${random}`.toUpperCase();
  }

  /**
   * Update Midtrans data for a transaction
   * @param {string} transactionId - Transaction ID
   * @param {Object} midtransData - Midtrans data to update
   * @param {string} midtransData.midtrans_token - Midtrans Snap token
   * @param {string} midtransData.midtrans_redirect_url - Midtrans redirect URL
   * @returns {Promise<boolean>} Success status
   */
  static async updateMidtransData(transactionId, midtransData) {
    return new Promise((resolve, reject) => {
      const updates = [];
      const values = [];

      // Add Midtrans fields to update
      if (midtransData.midtrans_token !== undefined) {
        updates.push('midtrans_token = ?');
        values.push(midtransData.midtrans_token);
      }

      if (midtransData.midtrans_redirect_url !== undefined) {
        updates.push('midtrans_redirect_url = ?');
        values.push(midtransData.midtrans_redirect_url);
      }

      if (updates.length === 0) {
        return resolve(false); // No updates to perform
      }

      values.push(new Date().toISOString()); // updated_at
      values.push(transactionId); // WHERE condition

      const sql = `
        UPDATE transactions
        SET ${updates.join(', ')}, updated_at = ?
        WHERE id = ?
      `;

      db.run(sql, values, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  /**
   * Clean up expired pending transactions (older than 24 hours)
   * @returns {Promise<number>} Number of cleaned transactions
   */
  static async cleanupExpiredTransactions() {
    return new Promise((resolve, reject) => {
      // Calculate 24 hours ago
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);
      const cutoffTime = twentyFourHoursAgo.toISOString();

      const sql = `
        UPDATE transactions
        SET status = 'expired'
        WHERE status = 'pending'
        AND created_at < ?
      `;

      db.run(sql, [cutoffTime], function(err) {
        if (err) {
          reject(err);
        } else {
          // console.log(`🧹 Cleaned up ${this.changes} expired pending transactions`); // Removed for production
          resolve(this.changes);
        }
      });
    });
  }






}

module.exports = Transaction;
