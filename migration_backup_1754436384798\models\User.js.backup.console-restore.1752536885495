const { db, checkIfUsersExist } = require('../db/database');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
const cacheService = require('../services/cacheService');

class User {
  static findByEmail(email) {
    return new Promise((resolve, reject) => {
      db.get('SELECT * FROM users WHERE email = ?', [email], (err, row) => {
        if (err) {
          return reject(err);
        }
        resolve(row);
      });
    });
  }
  static findByUsername(username) {
    return new Promise((resolve, reject) => {
      db.get('SELECT * FROM users WHERE username = ?', [username], (err, row) => {
        if (err) {
          return reject(err);
        }
        resolve(row);
      });
    });
  }
  static findById(id) {
    return cacheService.cacheQuery(
      `user:${id}`,
      () => {
        return new Promise((resolve, reject) => {
          db.get('SELECT * FROM users WHERE id = ?', [id], (err, row) => {
            if (err) {
              console.error('Database error in findById:', err);
              return reject(err);
            }
            resolve(row);
          });
        });
      },
      300000 // 5 minutes cache
    );
  }
  static async create(userData) {
    try {
      const hashedPassword = await bcrypt.hash(userData.password, 10);
      const userId = userData.id || uuidv4();

      // Set default role and plan for new users
      const role = userData.role || 'user';
      const planType = userData.plan_type || 'Preview';

      // Default storage based on plan type
      const defaultStorage = planType === 'Preview' ? 1 : 2;

      return new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO users
           (id, username, email, password, avatar_path, role, plan_type, max_streaming_slots, max_storage_gb)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            userId,
            userData.username,
            userData.email || null,
            hashedPassword,
            userData.avatar_path,
            role,
            planType,
            userData.max_streaming_slots || 0,
            userData.max_storage_gb || defaultStorage
          ],
          function (err) {
            if (err) {
              console.error("DB error during user creation:", err);
              return reject(err);
            }
            // console.log("User created successfully with ID:", userId); // Removed for production
            resolve({
              id: userId,
              username: userData.username,
              email: userData.email,
              role: role,
              plan_type: planType
            });
          }
        );
      });
    } catch (error) {
      console.error("Error in User.create:", error);
      throw error;
    }
  }
  static update(userId, userData) {
    const fields = [];
    const values = [];
    Object.entries(userData).forEach(([key, value]) => {
      fields.push(`${key} = ?`);
      values.push(value);
    });
    fields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(userId);
    const query = `UPDATE users SET ${fields.join(', ')} WHERE id = ?`;
    return new Promise((resolve, reject) => {
      db.run(query, values, function (err) {
        if (err) {
          return reject(err);
        }
        // Invalidate user cache
        cacheService.delete(`user:${userId}`);
        cacheService.delete(`user:stats:${userId}`);
        resolve({ id: userId, ...userData });
      });
    });
  }
  static async verifyPassword(plainPassword, hashedPassword) {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  // Get all users (admin only)
  static findAll(limit = 50, offset = 0, search = '') {
    return new Promise((resolve, reject) => {
      let query = `SELECT id, username, email, role, plan_type, max_streaming_slots, max_storage_gb,
                          used_storage_gb, is_active, created_at, updated_at,
                          trial_start_date, trial_end_date, trial_slots, trial_storage_gb
                   FROM users
                   WHERE id IS NOT NULL AND id != ''`;

      const params = [];

      if (search && search.trim() !== '') {
        query += ` AND (
          username LIKE ? OR
          email LIKE ? OR
          role LIKE ? OR
          plan_type LIKE ?
        )`;
        const searchTerm = `%${search.trim()}%`;
        params.push(searchTerm, searchTerm, searchTerm, searchTerm);
      }

      query += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
      params.push(limit, offset);

      db.all(query, params, (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows);
      });
    });
  }

  // Get all users with trial info (admin only) - alias for findAll
  static findAllWithTrialInfo(limit = 50, offset = 0, search = '') {
    return this.findAll(limit, offset, search);
  }

  // Count total users
  static countAll(search = '') {
    return new Promise((resolve, reject) => {
      let query = `SELECT COUNT(*) as total FROM users WHERE id IS NOT NULL AND id != ''`;
      const params = [];

      if (search && search.trim() !== '') {
        query += ` AND (
          username LIKE ? OR
          email LIKE ? OR
          role LIKE ? OR
          plan_type LIKE ?
        )`;
        const searchTerm = `%${search.trim()}%`;
        params.push(searchTerm, searchTerm, searchTerm, searchTerm);
      }

      db.get(query, params, (err, row) => {
        if (err) {
          return reject(err);
        }
        resolve(row.total || 0);
      });
    });
  }

  // Update user role
  static updateRole(userId, role) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE users SET role = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [role, userId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: userId, role });
        }
      );
    });
  }

  // Update user plan
  static updatePlan(userId, planType, maxSlots, maxStorage) {
    return new Promise((resolve, reject) => {
      db.run(
        `UPDATE users SET plan_type = ?, max_streaming_slots = ?, max_storage_gb = ?,
         updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        [planType, maxSlots, maxStorage, userId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: userId, plan_type: planType, max_streaming_slots: maxSlots, max_storage_gb: maxStorage });
        }
      );
    });
  }

  // Give trial to user
  static giveTrial(userId, durationDays, trialSlots = 1, trialStorageGB = 0.05) {
    return new Promise((resolve, reject) => {
      const startDate = new Date().toISOString();
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + durationDays);

      db.run(
        `UPDATE users SET
         trial_start_date = ?,
         trial_end_date = ?,
         trial_slots = ?,
         trial_storage_gb = ?,
         max_streaming_slots = ?,
         max_storage_gb = ?,
         updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [startDate, endDate.toISOString(), trialSlots, trialStorageGB, trialSlots, trialStorageGB, userId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({
            id: userId,
            trial_start_date: startDate,
            trial_end_date: endDate.toISOString(),
            trial_slots: trialSlots,
            trial_storage_gb: trialStorageGB
          });
        }
      );
    });
  }

  // Check if user has active trial
  static hasActiveTrial(userId) {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT trial_start_date, trial_end_date, trial_slots, trial_storage_gb
         FROM users WHERE id = ?`,
        [userId],
        (err, row) => {
          if (err) {
            return reject(err);
          }

          if (!row || !row.trial_start_date || !row.trial_end_date) {
            resolve(null);
            return;
          }

          const now = new Date();
          const trialEnd = new Date(row.trial_end_date);

          if (trialEnd > now) {
            resolve({
              trial_start_date: row.trial_start_date,
              trial_end_date: row.trial_end_date,
              trial_slots: row.trial_slots,
              trial_storage_gb: row.trial_storage_gb,
              is_active: true
            });
          } else {
            resolve(null);
          }
        }
      );
    });
  }

  // Remove expired trial
  static async removeExpiredTrial(userId) {
    try {
      // Get Preview plan details to ensure consistent storage limits
      const Subscription = require('./Subscription');
      const previewPlan = await Subscription.getPlanByName('Preview');

      if (!previewPlan) {
        throw new Error('Preview plan not found for trial removal');
      }

      return new Promise((resolve, reject) => {
        db.run(
          `UPDATE users SET
           trial_start_date = NULL,
           trial_end_date = NULL,
           trial_slots = 0,
           trial_storage_gb = 0,
           max_streaming_slots = ?,
           max_storage_gb = ?,
           updated_at = CURRENT_TIMESTAMP
           WHERE id = ?`,
          [previewPlan.max_streaming_slots, previewPlan.max_storage_gb, userId],
          function (err) {
            if (err) {
              return reject(err);
            }
            resolve({
              id: userId,
              trial_removed: true,
              reset_to_preview_plan: {
                max_streaming_slots: previewPlan.max_streaming_slots,
                max_storage_gb: previewPlan.max_storage_gb
              }
            });
          }
        );
      });
    } catch (error) {
      throw new Error(`Failed to remove expired trial: ${error.message}`);
    }
  }

  // Activate/Deactivate user
  static updateActiveStatus(userId, isActive) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE users SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [isActive ? 1 : 0, userId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: userId, is_active: isActive });
        }
      );
    });
  }

  // Get user statistics (optimized single query)
  static getUserStats(userId) {
    return cacheService.cacheQuery(
      `user:stats:${userId}`,
      () => {
        return new Promise((resolve, reject) => {
          // Optimized single query instead of multiple subqueries
          db.get(
            `SELECT
               COUNT(DISTINCT v.id) as total_videos,
               COUNT(DISTINCT s.id) as total_streams,
               COUNT(DISTINCT CASE WHEN s.status = 'live' THEN s.id END) as active_streams,
               COALESCE(SUM(DISTINCT v.file_size), 0) as total_storage_bytes
             FROM users u
             LEFT JOIN videos v ON v.user_id = u.id
             LEFT JOIN streams s ON s.user_id = u.id
             WHERE u.id = ?`,
            [userId],
            (err, row) => {
              if (err) {
                return reject(err);
              }
              resolve({
                total_videos: row.total_videos || 0,
                total_streams: row.total_streams || 0,
                active_streams: row.active_streams || 0,
                total_storage_gb: row.total_storage_bytes ? (row.total_storage_bytes / (1024 * 1024 * 1024)).toFixed(2) : 0
              });
            }
          );
        });
      },
      60000 // 1 minute cache for stats
    );
  }

  // Check if user exists by email
  static async existsByEmail(email) {
    try {
      const user = await this.findByEmail(email);
      return !!user;
    } catch (error) {
      return false;
    }
  }

  // Check if user exists by username
  static async existsByUsername(username) {
    try {
      const user = await this.findByUsername(username);
      return !!user;
    } catch (error) {
      return false;
    }
  }

  // Update user information
  static updateUser(userId, updateData) {
    return new Promise((resolve, reject) => {
      const fields = [];
      const values = [];

      // Build dynamic update query
      Object.keys(updateData).forEach(key => {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      });

      // Add updated_at timestamp
      fields.push('updated_at = CURRENT_TIMESTAMP');
      values.push(userId);

      const query = `UPDATE users SET ${fields.join(', ')} WHERE id = ?`;

      db.run(query, values, function (err) {
        if (err) {
          return reject(err);
        }
        resolve({ userId, changes: this.changes });
      });
    });
  }

  // Delete user and all related data
  static deleteUser(userId) {
    return new Promise((resolve, reject) => {
      // Start transaction to ensure data consistency
      db.serialize(() => {
        db.run('BEGIN TRANSACTION', (err) => {
          if (err) {
            return reject(err);
          }

          // Delete user's videos first (to handle file cleanup)
          db.all('SELECT filepath, thumbnail_path FROM videos WHERE user_id = ?', [userId], (err, videos) => {
            if (err) {
              db.run('ROLLBACK');
              return reject(err);
            }

            // Delete video files from filesystem
            const fs = require('fs');
            const path = require('path');
            videos.forEach(video => {
              // Delete video file
              if (video.filepath) {
                try {
                  const fullPath = path.join(process.cwd(), 'public', video.filepath);
                  if (fs.existsSync(fullPath)) {
                    fs.unlinkSync(fullPath);
                  }
                } catch (fileErr) {
                  console.error('Error deleting video file:', fileErr);
                  // Continue with database cleanup even if file deletion fails
                }
              }

              // Delete thumbnail file
              if (video.thumbnail_path) {
                try {
                  const thumbnailPath = path.join(process.cwd(), 'public', video.thumbnail_path);
                  if (fs.existsSync(thumbnailPath)) {
                    fs.unlinkSync(thumbnailPath);
                  }
                } catch (fileErr) {
                  console.error('Error deleting thumbnail file:', fileErr);
                  // Continue with database cleanup even if file deletion fails
                }
              }
            });

            // Delete from database tables in correct order (respecting foreign keys)
            const deleteQueries = [
              'DELETE FROM videos WHERE user_id = ?',
              'DELETE FROM streams WHERE user_id = ?',
              'DELETE FROM stream_history WHERE user_id = ?',
              'DELETE FROM user_subscriptions WHERE user_id = ?',
              'DELETE FROM transactions WHERE user_id = ?',
              'DELETE FROM notifications WHERE target_user_id = ?',
              'DELETE FROM users WHERE id = ?'
            ];

            let completed = 0;
            let hasError = false;

            deleteQueries.forEach((query, index) => {
              db.run(query, [userId], function(err) {
                if (err && !hasError) {
                  hasError = true;
                  db.run('ROLLBACK');
                  return reject(err);
                }

                completed++;
                if (completed === deleteQueries.length && !hasError) {
                  db.run('COMMIT', (commitErr) => {
                    if (commitErr) {
                      return reject(commitErr);
                    }

                    // Clear user cache
                    cacheService.delete(`user:${userId}`);
                    cacheService.delete(`user:stats:${userId}`);

                    resolve({ userId, deleted: true });
                  });
                }
              });
            });
          });
        });
      });
    });
  }
}

module.exports = User;