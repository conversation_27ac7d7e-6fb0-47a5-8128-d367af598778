/**
 * Supabase Query Conversion Examples for StreamOnPod
 * 
 * This file provides examples of converting SQLite queries to Supabase operations.
 * Use these patterns to refactor your existing code.
 */

const supabaseDb = require('./supabase-database-adapter');

// ============================================================================
// USER OPERATIONS
// ============================================================================

// SQLite: Create user
// OLD: db.run('INSERT INTO users (id, username, email, password) VALUES (?, ?, ?, ?)', [id, username, email, password])
// NEW:
async function createUser(userData) {
  return await supabaseDb.createUser({
    id: userData.id,
    username: userData.username,
    email: userData.email,
    password: userData.password,
    created_at: new Date().toISOString()
  });
}

// SQLite: Get user by ID
// OLD: db.get('SELECT * FROM users WHERE id = ?', [userId], callback)
// NEW:
async function getUserById(userId) {
  return await supabaseDb.getUserById(userId);
}

// SQLite: Get user by username
// OLD: db.get('SELECT * FROM users WHERE username = ?', [username], callback)
// NEW:
async function getUserByUsername(username) {
  return await supabaseDb.getUserByUsername(username);
}

// SQLite: Update user
// OLD: db.run('UPDATE users SET email = ?, updated_at = ? WHERE id = ?', [email, timestamp, userId])
// NEW:
async function updateUser(userId, updates) {
  return await supabaseDb.updateUser(userId, {
    ...updates,
    updated_at: new Date().toISOString()
  });
}

// ============================================================================
// STREAM OPERATIONS
// ============================================================================

// SQLite: Create stream
// OLD: db.run('INSERT INTO streams (id, title, user_id, rtmp_url, stream_key) VALUES (?, ?, ?, ?, ?)', [id, title, userId, rtmpUrl, streamKey])
// NEW:
async function createStream(streamData) {
  return await supabaseDb.createStream({
    id: streamData.id,
    title: streamData.title,
    user_id: streamData.userId,
    rtmp_url: streamData.rtmpUrl,
    stream_key: streamData.streamKey,
    status: 'offline',
    created_at: new Date().toISOString()
  });
}

// SQLite: Get streams by user
// OLD: db.all('SELECT * FROM streams WHERE user_id = ? ORDER BY created_at DESC', [userId], callback)
// NEW:
async function getStreamsByUser(userId) {
  return await supabaseDb.getStreamsByUserId(userId);
}

// SQLite: Update stream status
// OLD: db.run('UPDATE streams SET status = ?, status_updated_at = ? WHERE id = ?', [status, timestamp, streamId])
// NEW:
async function updateStreamStatus(streamId, status) {
  return await supabaseDb.updateStream(streamId, {
    status: status,
    status_updated_at: new Date().toISOString()
  });
}

// SQLite: Get active streams
// OLD: db.all('SELECT * FROM streams WHERE status = ?', ['live'], callback)
// NEW:
async function getActiveStreams() {
  const { data, error } = await supabaseDb.supabase
    .from('streams')
    .select('*')
    .eq('status', 'live');
  
  if (error) throw error;
  return data || [];
}

// ============================================================================
// VIDEO OPERATIONS
// ============================================================================

// SQLite: Create video
// OLD: db.run('INSERT INTO videos (id, title, filepath, user_id) VALUES (?, ?, ?, ?)', [id, title, filepath, userId])
// NEW:
async function createVideo(videoData) {
  return await supabaseDb.createVideo({
    id: videoData.id,
    title: videoData.title,
    filepath: videoData.filepath,
    user_id: videoData.userId,
    file_size: videoData.fileSize,
    duration: videoData.duration,
    created_at: new Date().toISOString()
  });
}

// SQLite: Get videos by user
// OLD: db.all('SELECT * FROM videos WHERE user_id = ? ORDER BY upload_date DESC', [userId], callback)
// NEW:
async function getVideosByUser(userId) {
  return await supabaseDb.getVideosByUserId(userId);
}

// ============================================================================
// SUBSCRIPTION OPERATIONS
// ============================================================================

// SQLite: Get user subscription with plan details
// OLD: db.get(`SELECT us.*, sp.name, sp.price, sp.max_streaming_slots 
//              FROM user_subscriptions us 
//              JOIN subscription_plans sp ON us.plan_id = sp.id 
//              WHERE us.user_id = ? AND us.is_active = 1`, [userId], callback)
// NEW:
async function getUserSubscriptionWithPlan(userId) {
  return await supabaseDb.getUserSubscription(userId);
}

// SQLite: Get all subscription plans
// OLD: db.all('SELECT * FROM subscription_plans WHERE is_active = 1 ORDER BY price ASC', callback)
// NEW:
async function getSubscriptionPlans() {
  return await supabaseDb.getSubscriptionPlans();
}

// SQLite: Create subscription
// OLD: db.run('INSERT INTO user_subscriptions (user_id, plan_id, start_date, end_date) VALUES (?, ?, ?, ?)', [userId, planId, startDate, endDate])
// NEW:
async function createSubscription(subscriptionData) {
  return await supabaseDb.createSubscription({
    user_id: subscriptionData.userId,
    plan_id: subscriptionData.planId,
    start_date: subscriptionData.startDate,
    end_date: subscriptionData.endDate,
    is_active: true,
    created_at: new Date().toISOString()
  });
}

// ============================================================================
// TRANSACTION OPERATIONS
// ============================================================================

// SQLite: Create transaction
// OLD: db.run('INSERT INTO transactions (id, user_id, amount, currency, status) VALUES (?, ?, ?, ?, ?)', [id, userId, amount, currency, status])
// NEW:
async function createTransaction(transactionData) {
  return await supabaseDb.createTransaction({
    id: transactionData.id,
    user_id: transactionData.userId,
    amount: transactionData.amount,
    currency: transactionData.currency,
    status: transactionData.status,
    payment_method: transactionData.paymentMethod,
    created_at: new Date().toISOString()
  });
}

// SQLite: Get user transactions
// OLD: db.all('SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC', [userId], callback)
// NEW:
async function getUserTransactions(userId) {
  return await supabaseDb.getTransactionsByUserId(userId);
}

// ============================================================================
// COMPLEX QUERIES
// ============================================================================

// SQLite: Get user with subscription and usage stats
// OLD: Complex JOIN query with multiple tables
// NEW: Use multiple queries or database functions
async function getUserWithStats(userId) {
  try {
    // Get user data
    const user = await supabaseDb.getUserById(userId);
    if (!user) return null;

    // Get subscription data
    const subscription = await supabaseDb.getUserSubscription(userId);

    // Get usage stats
    const { data: streamCount } = await supabaseDb.supabase
      .from('streams')
      .select('count')
      .eq('user_id', userId);

    const { data: videoCount } = await supabaseDb.supabase
      .from('videos')
      .select('count')
      .eq('user_id', userId);

    return {
      ...user,
      subscription,
      stats: {
        streamCount: streamCount?.[0]?.count || 0,
        videoCount: videoCount?.[0]?.count || 0
      }
    };
  } catch (error) {
    throw new Error(`Failed to get user stats: ${error.message}`);
  }
}

// SQLite: Search streams by title
// OLD: db.all('SELECT * FROM streams WHERE title LIKE ? ORDER BY created_at DESC', [`%${searchTerm}%`], callback)
// NEW:
async function searchStreams(searchTerm) {
  const { data, error } = await supabaseDb.supabase
    .from('streams')
    .select('*')
    .ilike('title', `%${searchTerm}%`)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data || [];
}

// ============================================================================
// AUTHENTICATION INTEGRATION
// ============================================================================

// If using Supabase Auth, you can integrate it like this:
async function signUpUser(email, password, userData) {
  try {
    // Create auth user
    const { data: authData, error: authError } = await supabaseDb.supabase.auth.signUp({
      email,
      password
    });

    if (authError) throw authError;

    // Create user profile
    const userProfile = await supabaseDb.createUser({
      id: authData.user.id,
      email: authData.user.email,
      username: userData.username,
      ...userData
    });

    return { authData, userProfile };
  } catch (error) {
    throw new Error(`User signup failed: ${error.message}`);
  }
}

async function signInUser(email, password) {
  try {
    const { data, error } = await supabaseDb.supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) throw error;

    // Get user profile
    const userProfile = await supabaseDb.getUserById(data.user.id);

    return { authData: data, userProfile };
  } catch (error) {
    throw new Error(`User signin failed: ${error.message}`);
  }
}

// ============================================================================
// REAL-TIME SUBSCRIPTIONS
// ============================================================================

// Supabase provides real-time subscriptions for live updates
function subscribeToStreamUpdates(callback) {
  return supabaseDb.supabase
    .channel('stream-updates')
    .on('postgres_changes', {
      event: '*',
      schema: 'public',
      table: 'streams'
    }, callback)
    .subscribe();
}

function subscribeToUserNotifications(userId, callback) {
  return supabaseDb.supabase
    .channel('user-notifications')
    .on('postgres_changes', {
      event: 'INSERT',
      schema: 'public',
      table: 'notifications',
      filter: `user_id=eq.${userId}`
    }, callback)
    .subscribe();
}

// ============================================================================
// MIGRATION HELPERS
// ============================================================================

// Helper function to convert SQLite callback pattern to async/await
function promisifyQuery(queryFunction) {
  return function(...args) {
    return new Promise((resolve, reject) => {
      const callback = (err, result) => {
        if (err) reject(err);
        else resolve(result);
      };
      queryFunction(...args, callback);
    });
  };
}

// Helper function to batch operations
async function batchOperations(operations, batchSize = 100) {
  const results = [];
  
  for (let i = 0; i < operations.length; i += batchSize) {
    const batch = operations.slice(i, i + batchSize);
    const batchResults = await Promise.all(batch);
    results.push(...batchResults);
  }
  
  return results;
}

module.exports = {
  // User operations
  createUser,
  getUserById,
  getUserByUsername,
  updateUser,
  
  // Stream operations
  createStream,
  getStreamsByUser,
  updateStreamStatus,
  getActiveStreams,
  
  // Video operations
  createVideo,
  getVideosByUser,
  
  // Subscription operations
  getUserSubscriptionWithPlan,
  getSubscriptionPlans,
  createSubscription,
  
  // Transaction operations
  createTransaction,
  getUserTransactions,
  
  // Complex queries
  getUserWithStats,
  searchStreams,
  
  // Authentication
  signUpUser,
  signInUser,
  
  // Real-time
  subscribeToStreamUpdates,
  subscribeToUserNotifications,
  
  // Helpers
  promisifyQuery,
  batchOperations
};
