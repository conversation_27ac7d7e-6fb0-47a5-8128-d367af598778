// Supabase Database Connection for StreamOnPod
const supabaseAdapter = require('../supabase-database-adapter');
const bcrypt = require('bcrypt');

// Initialize Supabase connection with retry logic and fallback
async function initializeDatabase() {
  const maxRetries = 5;
  const baseDelay = 1000; // 1 second

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`[DB Init] Attempt ${attempt}/${maxRetries} - Checking Supabase connection...`);

      const healthCheck = await supabaseAdapter.healthCheck();
      if (healthCheck.healthy) {
        console.log('✅ Supabase database connection established');

        // Insert default data if needed (with error handling)
        try {
          await insertDefaultPlans();
          await insertDefaultPermissions();
          console.log('✅ Database initialization completed successfully');
        } catch (dataError) {
          console.warn('⚠️  Default data insertion failed (non-critical):', dataError.message);
          // Continue anyway - the connection is working
        }

        return; // Success, exit retry loop
      } else {
        throw new Error(`Health check failed: ${healthCheck.error}`);
      }
    } catch (error) {
      const isLastAttempt = attempt === maxRetries;

      if (isLastAttempt) {
        console.error('❌ All database initialization attempts failed');
        console.error('❌ Final error:', error.message);
        console.warn('⚠️  Application will continue with limited database functionality');

        // Don't throw - allow app to start with degraded functionality
        return;
      } else {
        const delay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff
        console.warn(`⚠️  Attempt ${attempt} failed: ${error.message}`);
        console.log(`⏳ Retrying in ${delay}ms...`);

        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
}

// Initialize database on module load with graceful error handling
initializeDatabase().catch(error => {
  console.error('❌ Database initialization error (non-fatal):', error.message);
  console.log('🔄 Application will continue - database operations may retry automatically');
});
// Supabase tables are created via schema migration files
// This function is kept for compatibility but doesn't create tables
async function createTables() {
  console.log('📋 Tables are managed by Supabase schema - skipping table creation');
  return true;
}

async function addTrialColumns() {
  // Trial columns are already included in Supabase schema
  console.log('📋 Trial columns already exist in Supabase schema');
  return true;
}

async function addReferralColumns() {
  // Referral columns are already included in Supabase schema
  console.log('📋 Referral columns already exist in Supabase schema');
  return true;
}

async function insertDefaultPlans() {
  try {
    // Use retry logic for checking existing plans
    const existingPlans = await supabaseAdapter.executeWithRetry(
      () => supabaseAdapter.getSubscriptionPlans(),
      3,
      'subscription plans check'
    );

    if (existingPlans.length > 0) {
      console.log('📋 Subscription plans already exist in Supabase');
      return;
    }

    // Default plans data is handled by Supabase data migration
    console.log('📋 Default subscription plans will be inserted via Supabase data migration');
  } catch (error) {
    console.warn('⚠️  Could not check subscription plans (non-critical):', error.message);
  }
}

async function insertDefaultPermissions() {
  try {
    const { v4: uuidv4 } = require('uuid');

    // Check if permissions already exist with retry logic
    const checkPermissions = async () => {
      const { data, error } = await supabaseAdapter.supabase
        .from('role_permissions')
        .select('*')
        .limit(1);

      if (error) throw error;
      return data;
    };

    const existingPermissions = await supabaseAdapter.executeWithRetry(
      checkPermissions,
      3,
      'role permissions check'
    );

    if (existingPermissions && existingPermissions.length > 0) {
      console.log('📋 Role permissions already exist in Supabase');
      return;
    }

    const defaultPermissions = [
      // Admin permissions
      { id: uuidv4(), role: 'admin', permission: 'manage_users' },
      { id: uuidv4(), role: 'admin', permission: 'manage_plans' },
      { id: uuidv4(), role: 'admin', permission: 'view_all_streams' },
      { id: uuidv4(), role: 'admin', permission: 'manage_system' },
      { id: uuidv4(), role: 'admin', permission: 'unlimited_streaming' },
      { id: uuidv4(), role: 'admin', permission: 'unlimited_storage' },

      // User permissions
      { id: uuidv4(), role: 'user', permission: 'create_stream' },
      { id: uuidv4(), role: 'user', permission: 'upload_video' },
      { id: uuidv4(), role: 'user', permission: 'view_own_streams' },
      { id: uuidv4(), role: 'user', permission: 'manage_profile' },

      // Moderator permissions
      { id: uuidv4(), role: 'moderator', permission: 'view_all_streams' },
      { id: uuidv4(), role: 'moderator', permission: 'moderate_content' },
      { id: uuidv4(), role: 'moderator', permission: 'manage_profile' }
    ];

    const insertPermissions = async () => {
      const { error } = await supabaseAdapter.supabase
        .from('role_permissions')
        .insert(defaultPermissions);

      if (error) throw error;
    };

    await supabaseAdapter.executeWithRetry(
      insertPermissions,
      3,
      'default permissions insertion'
    );

    console.log('✅ Default permissions inserted into Supabase');
  } catch (error) {
    console.warn('⚠️  Could not insert default permissions (non-critical):', error.message);
  }
}

async function checkIfUsersExist() {
  try {
    const { data, error } = await supabaseAdapter.supabase
      .from('users')
      .select('id', { count: 'exact' })
      .limit(1);

    if (error) {
      throw error;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error('Error checking if users exist:', error.message);
    throw error;
  }
}

// Export Supabase adapter and utility functions
module.exports = {
  db: supabaseAdapter, // For backward compatibility
  supabase: supabaseAdapter.supabase, // Direct Supabase client access
  checkIfUsersExist,
  initializeDatabase,
  createTables,
  addTrialColumns,
  addReferralColumns,
  insertDefaultPlans,
  insertDefaultPermissions
};