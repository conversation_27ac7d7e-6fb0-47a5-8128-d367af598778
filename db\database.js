// Supabase Database Connection for StreamOnPod
const supabaseAdapter = require('../supabase-database-adapter');
const bcrypt = require('bcrypt');

// Initialize Supabase connection
async function initializeDatabase() {
  try {
    const healthCheck = await supabaseAdapter.healthCheck();
    if (healthCheck.healthy) {
      console.log('✅ Supabase database connection established');
      // Insert default data if needed
      await insertDefaultPlans();
      await insertDefaultPermissions();
    } else {
      console.error('❌ Supabase database health check failed:', healthCheck.error);
      throw new Error('Database connection failed');
    }
  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    throw error;
  }
}

// Initialize database on module load
initializeDatabase().catch(console.error);
// Supabase tables are created via schema migration files
// This function is kept for compatibility but doesn't create tables
async function createTables() {
  console.log('📋 Tables are managed by Supabase schema - skipping table creation');
  return true;
}

async function addTrialColumns() {
  // Trial columns are already included in Supabase schema
  console.log('📋 Trial columns already exist in Supabase schema');
  return true;
}

async function addReferralColumns() {
  // Referral columns are already included in Supabase schema
  console.log('📋 Referral columns already exist in Supabase schema');
  return true;
}

async function insertDefaultPlans() {
  try {
    // Check if plans already exist
    const existingPlans = await supabaseAdapter.getSubscriptionPlans();
    if (existingPlans.length > 0) {
      console.log('📋 Subscription plans already exist in Supabase');
      return;
    }

    // Default plans data is handled by Supabase data migration
    console.log('📋 Default subscription plans will be inserted via Supabase data migration');
  } catch (error) {
    console.error('Error checking subscription plans:', error.message);
  }
}

async function insertDefaultPermissions() {
  try {
    const { v4: uuidv4 } = require('uuid');

    // Check if permissions already exist
    const { data: existingPermissions } = await supabaseAdapter.supabase
      .from('role_permissions')
      .select('*')
      .limit(1);

    if (existingPermissions && existingPermissions.length > 0) {
      console.log('📋 Role permissions already exist in Supabase');
      return;
    }

    const defaultPermissions = [
      // Admin permissions
      { id: uuidv4(), role: 'admin', permission: 'manage_users' },
      { id: uuidv4(), role: 'admin', permission: 'manage_plans' },
      { id: uuidv4(), role: 'admin', permission: 'view_all_streams' },
      { id: uuidv4(), role: 'admin', permission: 'manage_system' },
      { id: uuidv4(), role: 'admin', permission: 'unlimited_streaming' },
      { id: uuidv4(), role: 'admin', permission: 'unlimited_storage' },

      // User permissions
      { id: uuidv4(), role: 'user', permission: 'create_stream' },
      { id: uuidv4(), role: 'user', permission: 'upload_video' },
      { id: uuidv4(), role: 'user', permission: 'view_own_streams' },
      { id: uuidv4(), role: 'user', permission: 'manage_profile' },

      // Moderator permissions
      { id: uuidv4(), role: 'moderator', permission: 'view_all_streams' },
      { id: uuidv4(), role: 'moderator', permission: 'moderate_content' },
      { id: uuidv4(), role: 'moderator', permission: 'manage_profile' }
    ];

    const { error } = await supabaseAdapter.supabase
      .from('role_permissions')
      .insert(defaultPermissions);

    if (error) {
      console.error('Error inserting default permissions:', error.message);
    } else {
      console.log('✅ Default permissions inserted into Supabase');
    }
  } catch (error) {
    console.error('Error in insertDefaultPermissions:', error.message);
  }
}

async function checkIfUsersExist() {
  try {
    const { data, error } = await supabaseAdapter.supabase
      .from('users')
      .select('id', { count: 'exact' })
      .limit(1);

    if (error) {
      throw error;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error('Error checking if users exist:', error.message);
    throw error;
  }
}

// Export Supabase adapter and utility functions
module.exports = {
  db: supabaseAdapter, // For backward compatibility
  supabase: supabaseAdapter.supabase, // Direct Supabase client access
  checkIfUsersExist,
  initializeDatabase,
  createTables,
  addTrialColumns,
  addReferralColumns,
  insertDefaultPlans,
  insertDefaultPermissions
};