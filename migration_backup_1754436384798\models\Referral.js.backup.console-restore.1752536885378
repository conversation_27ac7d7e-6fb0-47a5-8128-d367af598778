const { db } = require('../db/database');
const { v4: uuidv4 } = require('uuid');

class Referral {
  /**
   * Generate unique referral code for user
   * @param {string} userId - User ID
   * @returns {Promise<string>} Generated referral code
   */
  static async generateReferralCode(userId) {
    try {
      // Generate a unique 8-character code
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();

      // Check if code already exists in users table
      const existing = await new Promise((resolve, reject) => {
        db.get(
          `SELECT id FROM users WHERE referral_code = ?`,
          [code],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      if (existing) {
        // Recursively generate new code if exists
        return this.generateReferralCode(userId);
      }

      // Update user's referral code (no need to create referral record yet)
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE users SET referral_code = ? WHERE id = ?`,
          [code, userId],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // console.log(`✅ Generated referral code ${code} for user ${userId}`); // Removed for production
      return code;
    } catch (error) {
      console.error('Error generating referral code:', error);
      throw error;
    }
  }

  /**
   * Get referral by code
   * @param {string} code - Referral code
   * @returns {Promise<Object|null>} Referral data
   */
  static getReferralByCode(code) {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT u.id as referrer_id, u.username as referrer_username, u.referral_code
         FROM users u
         WHERE u.referral_code = ?`,
        [code],
        (err, row) => {
          if (err) reject(err);
          else resolve(row || null);
        }
      );
    });
  }

  /**
   * Get user's referral statistics
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Referral statistics
   */
  static async getUserReferralStats(userId) {
    try {
      // Get user's referral balance
      const user = await new Promise((resolve, reject) => {
        db.get(
          `SELECT referral_balance, referral_code FROM users WHERE id = ?`,
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      // Get total clicks
      const clicksResult = await new Promise((resolve, reject) => {
        db.get(
          `SELECT COUNT(*) as total_clicks
           FROM referral_clicks rc
           WHERE rc.referral_code = ?`,
          [user?.referral_code || ''],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      // Get pending referrals count (signed_up but not completed)
      const pendingResult = await new Promise((resolve, reject) => {
        db.get(
          `SELECT COUNT(*) as pending_count
           FROM referrals
           WHERE referrer_id = ? AND status = 'signed_up'`,
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      // Get successful referrals count
      const successResult = await new Promise((resolve, reject) => {
        db.get(
          `SELECT COUNT(*) as success_count 
           FROM referrals 
           WHERE referrer_id = ? AND status = 'completed'`,
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      return {
        balance: user?.referral_balance || 0,
        referral_code: user?.referral_code,
        total_clicks: clicksResult?.total_clicks || 0,
        pending_referrals: pendingResult?.pending_count || 0,
        successful_referrals: successResult?.success_count || 0
      };
    } catch (error) {
      console.error('Error getting user referral stats:', error);
      throw error;
    }
  }

  /**
   * Track referral click
   * @param {string} referralCode - Referral code
   * @param {string} ipAddress - IP address
   * @param {string} userAgent - User agent
   * @returns {Promise<void>}
   */
  static async trackClick(referralCode, ipAddress, userAgent) {
    try {
      const clickId = uuidv4();
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO referral_clicks (id, referral_code, ip_address, user_agent) 
           VALUES (?, ?, ?, ?)`,
          [clickId, referralCode, ipAddress, userAgent],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });
    } catch (error) {
      console.error('Error tracking referral click:', error);
      throw error;
    }
  }

  /**
   * Process referral signup
   * @param {string} referralCode - Referral code
   * @param {string} newUserId - New user ID
   * @returns {Promise<void>}
   */
  static async processReferralSignup(referralCode, newUserId) {
    try {
      // Get the referrer user by referral code
      const referrer = await new Promise((resolve, reject) => {
        db.get(
          `SELECT id FROM users WHERE referral_code = ?`,
          [referralCode],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      if (!referrer) {
        console.error('Referrer not found for code:', referralCode);
        return;
      }

      // Check if referral record already exists
      const existingReferral = await new Promise((resolve, reject) => {
        db.get(
          `SELECT id FROM referrals WHERE referrer_id = ? AND referee_id = ?`,
          [referrer.id, newUserId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      if (existingReferral) {
        // console.log('Referral record already exists for this user'); // Removed for production
        return;
      }

      // Create new referral record
      const referralId = uuidv4();
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO referrals (id, referrer_id, referee_id, referral_code, status)
           VALUES (?, ?, ?, ?, 'signed_up')`,
          [referralId, referrer.id, newUserId, referralCode],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Update user's referred_by field
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE users SET referred_by = ? WHERE id = ?`,
          [referralCode, newUserId],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      console.log(`✅ Referral record created: ${referrer.id} -> ${newUserId} (code: ${referralCode})`);
    } catch (error) {
      console.error('Error processing referral signup:', error);
      throw error;
    }
  }

  /**
   * Calculate commission based on plan price
   * @param {number} planPrice - Plan price in IDR
   * @returns {number} Commission amount (5% of plan price)
   */
  static calculateCommission(planPrice) {
    return Math.floor(planPrice * 0.05); // 5% commission
  }

  /**
   * Process referral commission when payment is successful
   * Only processes commission for the FIRST successful payment of a referred user
   * @param {string} transactionId - Transaction ID
   * @param {string} userId - User who made payment (referee)
   * @param {number} paidAmount - Amount paid
   * @returns {Promise<void>}
   */
  static async processCommission(transactionId, userId, paidAmount) {
    try {
      // Check if user was referred and if commission already paid
      const user = await new Promise((resolve, reject) => {
        db.get(
          `SELECT referred_by, first_commission_paid FROM users WHERE id = ?`,
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      if (!user?.referred_by) {
        // console.log(`ℹ️ User ${userId} was not referred - no commission to process`); // Removed for production
        return; // User was not referred
      }

      if (user.first_commission_paid) {
        console.log(`ℹ️ User ${userId} has already generated commission before - skipping (first purchase only policy)`);
        return; // Commission already paid for this user's first purchase
      }

      // Get referrer info
      const referrer = await this.getReferralByCode(user.referred_by);
      if (!referrer) {
        console.error('Referrer not found for code:', user.referred_by);
        return;
      }

      // Get referral record
      const referralRecord = await new Promise((resolve, reject) => {
        db.get(
          `SELECT id FROM referrals WHERE referrer_id = ? AND referee_id = ?`,
          [referrer.referrer_id, userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      if (!referralRecord) {
        console.error('Referral record not found for user:', userId);
        return;
      }

      // Calculate commission (5% of paid amount)
      const commissionAmount = this.calculateCommission(paidAmount);

      // Create earning record
      const earningId = uuidv4();
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO referral_earnings (id, user_id, referral_id, transaction_id, amount, status)
           VALUES (?, ?, ?, ?, ?, 'completed')`,
          [earningId, referrer.referrer_id, referralRecord.id, transactionId, commissionAmount],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Update referrer's balance
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE users
           SET referral_balance = referral_balance + ?
           WHERE id = ?`,
          [commissionAmount, referrer.referrer_id],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Update referral status to completed
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE referrals
           SET status = 'completed', commission_amount = ?, commission_paid = 1, completed_at = CURRENT_TIMESTAMP
           WHERE id = ?`,
          [commissionAmount, referralRecord.id],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Mark user as having received first commission
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE users SET first_commission_paid = 1 WHERE id = ?`,
          [userId],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      console.log(`✅ Referral commission processed: ${commissionAmount} IDR for user ${referrer.referrer_id} (first purchase only)`);
    } catch (error) {
      console.error('Error processing referral commission:', error);
      throw error;
    }
  }

  /**
   * Get user's referral earnings history
   * @param {string} userId - User ID
   * @param {number} limit - Limit results
   * @param {number} offset - Offset for pagination
   * @returns {Promise<Array>} Earnings history with masked usernames
   */
  static async getUserEarnings(userId, limit = 20, offset = 0) {
    try {
      const earnings = await new Promise((resolve, reject) => {
        db.all(
          `SELECT re.*, r.referral_code, u.username as referee_username, t.order_id
           FROM referral_earnings re
           JOIN referrals r ON re.referral_id = r.id
           LEFT JOIN users u ON r.referee_id = u.id
           LEFT JOIN transactions t ON re.transaction_id = t.id
           WHERE re.user_id = ?
           ORDER BY re.created_at DESC
           LIMIT ? OFFSET ?`,
          [userId, limit, offset],
          (err, rows) => {
            if (err) reject(err);
            else resolve(rows || []);
          }
        );
      });

      // Helper function to mask username with better uniqueness
      const maskUsername = (username) => {
        if (!username || username.length === 0) return 'U***';

        if (username.length === 1) {
          return username + '***';
        } else if (username.length === 2) {
          return username[0] + '*' + username[1];
        } else if (username.length === 3) {
          return username[0] + '*' + username[2];
        } else if (username.length === 4) {
          return username.substring(0, 2) + '**';
        } else if (username.length <= 6) {
          return username.substring(0, 2) + '*'.repeat(username.length - 3) + username[username.length - 1];
        } else {
          // For longer usernames, show first 2, last 1, and mask middle
          return username.substring(0, 2) + '*'.repeat(username.length - 3) + username[username.length - 1];
        }
      };

      // Process earnings with masked usernames and unique IDs
      const processedEarnings = earnings.map((earning, index) => ({
        ...earning,
        masked_username: maskUsername(earning.referee_username),
        unique_id: `#${String(index + 1).padStart(2, '0')}` // #01, #02, etc.
      }));

      return processedEarnings;
    } catch (error) {
      console.error('Error getting user earnings:', error);
      throw error;
    }
  }

  /**
   * Get detailed referral list with masked usernames
   * @param {string} userId - User ID (referrer)
   * @returns {Promise<Object>} Object containing pending and successful referrals
   */
  static async getUserReferralDetails(userId) {
    try {
      // Helper function to mask username with better uniqueness
      const maskUsername = (username) => {
        if (!username || username.length === 0) return 'U***';

        if (username.length === 1) {
          return username + '***';
        } else if (username.length === 2) {
          return username[0] + '*' + username[1];
        } else if (username.length === 3) {
          return username[0] + '*' + username[2];
        } else if (username.length === 4) {
          return username.substring(0, 2) + '**';
        } else if (username.length <= 6) {
          return username.substring(0, 2) + '*'.repeat(username.length - 3) + username[username.length - 1];
        } else {
          // For longer usernames, show first 2, last 1, and mask middle
          return username.substring(0, 2) + '*'.repeat(username.length - 3) + username[username.length - 1];
        }
      };

      // Get pending referrals (signed_up but not completed)
      const pendingReferrals = await new Promise((resolve, reject) => {
        db.all(
          `SELECT r.*, u.username, r.created_at as signup_date
           FROM referrals r
           LEFT JOIN users u ON r.referee_id = u.id
           WHERE r.referrer_id = ? AND r.status = 'signed_up'
           ORDER BY r.created_at DESC`,
          [userId],
          (err, rows) => {
            if (err) reject(err);
            else resolve(rows || []);
          }
        );
      });

      // Get successful referrals (completed with commission)
      const successfulReferrals = await new Promise((resolve, reject) => {
        db.all(
          `SELECT r.*, u.username, r.completed_at, r.commission_amount
           FROM referrals r
           LEFT JOIN users u ON r.referee_id = u.id
           WHERE r.referrer_id = ? AND r.status = 'completed'
           ORDER BY r.completed_at DESC`,
          [userId],
          (err, rows) => {
            if (err) reject(err);
            else resolve(rows || []);
          }
        );
      });

      // Process and mask usernames with unique identifiers
      const processedPending = pendingReferrals.map((ref, index) => ({
        id: ref.id,
        masked_username: maskUsername(ref.username),
        unique_id: `#${String(index + 1).padStart(2, '0')}`, // #01, #02, etc.
        signup_date: ref.signup_date,
        referral_code: ref.referral_code
      }));

      const processedSuccessful = successfulReferrals.map((ref, index) => ({
        id: ref.id,
        masked_username: maskUsername(ref.username),
        unique_id: `#${String(index + 1).padStart(2, '0')}`, // #01, #02, etc.
        completed_date: ref.completed_at,
        commission_amount: ref.commission_amount,
        referral_code: ref.referral_code
      }));

      return {
        pending: processedPending,
        successful: processedSuccessful
      };
    } catch (error) {
      console.error('Error getting user referral details:', error);
      throw error;
    }
  }

  /**
   * Create withdrawal request
   * @param {Object} withdrawalData - Withdrawal request data
   * @returns {Promise<Object>} Created withdrawal request
   */
  static async createWithdrawalRequest(withdrawalData) {
    try {
      const { userId, amount, bankName, accountNumber, accountName } = withdrawalData;
      
      // Check if user has sufficient balance
      const user = await new Promise((resolve, reject) => {
        db.get(
          `SELECT referral_balance FROM users WHERE id = ?`,
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      if (!user || user.referral_balance < amount) {
        throw new Error('Insufficient balance');
      }

      if (amount < 50000) {
        throw new Error('Minimum withdrawal amount is Rp 50,000');
      }

      // Create withdrawal request
      const requestId = uuidv4();
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO withdrawal_requests (id, user_id, amount, bank_name, account_number, account_name) 
           VALUES (?, ?, ?, ?, ?, ?)`,
          [requestId, userId, amount, bankName, accountNumber, accountName],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Deduct amount from user balance (pending withdrawal)
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE users SET referral_balance = referral_balance - ? WHERE id = ?`,
          [amount, userId],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      return { id: requestId, ...withdrawalData };
    } catch (error) {
      console.error('Error creating withdrawal request:', error);
      throw error;
    }
  }

  /**
   * Clean up orphaned referral records and ensure data consistency
   * @returns {Promise<void>}
   */
  static async cleanupReferralData() {
    try {
      // Remove referral records that don't have corresponding users
      await new Promise((resolve, reject) => {
        db.run(
          `DELETE FROM referrals
           WHERE referrer_id NOT IN (SELECT id FROM users)
           OR (referee_id IS NOT NULL AND referee_id NOT IN (SELECT id FROM users))`,
          function(err) {
            if (err) reject(err);
            else {
              // console.log(`✅ Cleaned up ${this.changes} orphaned referral records`); // Removed for production
              resolve();
            }
          }
        );
      });

      // Remove referral records that were created during code generation (old system)
      await new Promise((resolve, reject) => {
        db.run(
          `DELETE FROM referrals WHERE referee_id IS NULL AND status = 'pending'`,
          function(err) {
            if (err) reject(err);
            else {
              // console.log(`✅ Cleaned up ${this.changes} old referral generation records`); // Removed for production
              resolve();
            }
          }
        );
      });

      // console.log('✅ Referral data cleanup completed'); // Removed for production
    } catch (error) {
      console.error('Error cleaning up referral data:', error);
      throw error;
    }
  }

  /**
   * Migrate existing users to mark those who have already made successful payments
   * This ensures the "first purchase only" policy works correctly for existing data
   * @returns {Promise<void>}
   */
  static async migrateFirstCommissionFlags() {
    try {
      // Mark users who have successful transactions as having received first commission
      const result = await new Promise((resolve, reject) => {
        db.run(
          `UPDATE users
           SET first_commission_paid = 1
           WHERE id IN (
             SELECT DISTINCT user_id
             FROM transactions
             WHERE status = 'success'
           ) AND referred_by IS NOT NULL`,
          function(err) {
            if (err) reject(err);
            else resolve(this.changes);
          }
        );
      });

      // console.log(`✅ Migrated ${result} existing users with first_commission_paid flag`); // Removed for production
    } catch (error) {
      console.error('Error migrating first commission flags:', error);
      throw error;
    }
  }
}

module.exports = Referral;
