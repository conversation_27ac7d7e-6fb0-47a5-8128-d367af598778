[2025-08-05T23:26:24.794Z] 
📦 Step 1: Creating migration backup...
[2025-08-05T23:26:24.797Z] ✅ Database backup created: ./db/streamonpod_pre_supabase_1754436384788.db
[2025-08-05T23:26:24.801Z] ✅ Backed up: app.js
[2025-08-05T23:26:24.804Z] ✅ Backed up: package.json
[2025-08-05T23:26:24.806Z] ✅ Backed up: .env
[2025-08-05T23:26:24.808Z] ✅ Backed up: db/database.js
[2025-08-05T23:26:24.884Z] ✅ Backed up: models/
[2025-08-05T23:26:24.922Z] ✅ Backed up: middleware/
[2025-08-05T23:26:24.923Z] ✅ Application backup created: ./migration_backup_1754436384798
[2025-08-05T23:26:24.924Z] 
🔍 Step 2: Analyzing current database...
[2025-08-05T23:26:24.934Z] 📋 Found 13 tables to migrate:
[2025-08-05T23:26:24.941Z]    ✅ referrals: 9 columns, 0 rows
[2025-08-05T23:26:24.941Z]    ✅ role_permissions: 4 columns, 117 rows
[2025-08-05T23:26:24.941Z]    ✅ referral_earnings: 7 columns, 0 rows
[2025-08-05T23:26:24.942Z]    ✅ withdrawal_requests: 11 columns, 0 rows
[2025-08-05T23:26:24.942Z]    ✅ streams: 23 columns, 7 rows
[2025-08-05T23:26:24.943Z]    ✅ notifications: 11 columns, 37 rows
[2025-08-05T23:26:24.943Z]    ✅ subscription_plans: 11 columns, 5 rows
[2025-08-05T23:26:24.944Z]    ✅ users: 24 columns, 93 rows
[2025-08-05T23:26:24.944Z]    ✅ transactions: 14 columns, 88 rows
[2025-08-05T23:26:24.944Z]    ✅ videos: 19 columns, 28 rows
[2025-08-05T23:26:24.945Z]    ✅ stream_history: 16 columns, 0 rows
[2025-08-05T23:26:24.945Z]    ✅ referral_clicks: 5 columns, 0 rows
[2025-08-05T23:26:24.945Z]    ✅ user_subscriptions: 10 columns, 113 rows
[2025-08-05T23:26:24.946Z] 
🏗️  Step 3: Generating Supabase schema...
[2025-08-05T23:26:24.952Z] ✅ Supabase schema generated: ./supabase_schema.sql
[2025-08-05T23:26:24.952Z] 
📤 Step 4: Exporting data for migration...
[2025-08-05T23:26:24.973Z] ✅ Exported 7 records from streams
[2025-08-05T23:26:24.977Z] ✅ Exported 88 records from transactions
[2025-08-05T23:26:24.982Z] ✅ Exported 93 records from users
[2025-08-05T23:26:24.983Z] ✅ Exported 117 records from role_permissions
[2025-08-05T23:26:24.984Z] ✅ Exported 5 records from subscription_plans
[2025-08-05T23:26:24.985Z] ✅ Exported 28 records from videos
[2025-08-05T23:26:24.988Z] ✅ Exported 113 records from user_subscriptions
[2025-08-05T23:26:24.989Z] ✅ Exported 37 records from notifications
[2025-08-05T23:26:24.992Z] ✅ Data export completed: ./supabase_data.sql
[2025-08-05T23:26:24.993Z] 
📋 Step 5: Generating migration instructions...
[2025-08-05T23:26:24.994Z] ✅ Migration instructions generated: SUPABASE_MIGRATION_INSTRUCTIONS.md
[2025-08-05T23:26:24.995Z] ✅ Migration preparation completed successfully!
[2025-08-05T23:26:24.995Z] 📋 Next steps: Follow the generated migration instructions