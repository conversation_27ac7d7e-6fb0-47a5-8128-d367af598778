/**
 * Supabase Database Adapter for StreamOnPod
 * 
 * This adapter replaces the SQLite database connection with Supabase client
 * and provides compatibility methods for existing code.
 */

const { createClient } = require('@supabase/supabase-js');

class SupabaseDatabaseAdapter {
  constructor() {
    this.supabase = null;
    this.isConnected = false;
    this.init();
  }

  init() {
    try {
      const supabaseUrl = process.env.SUPABASE_URL;
      const supabaseKey = process.env.SUPABASE_SERVICE_KEY; // Use service key for server-side operations
      
      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Supabase credentials not found in environment variables');
      }

      this.supabase = createClient(supabaseUrl, supabaseKey, {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      });

      this.isConnected = true;
      console.log('✅ Supabase database adapter initialized');
    } catch (error) {
      console.error('❌ Supabase initialization failed:', error.message);
      throw error;
    }
  }

  // Graceful database operation wrapper with retry logic
  async executeWithRetry(operation, maxRetries = 3, operationName = 'database operation') {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        const isLastAttempt = attempt === maxRetries;
        const isNetworkError = error.message.includes('fetch failed') ||
                              error.message.includes('network') ||
                              error.message.includes('timeout');

        if (isNetworkError && !isLastAttempt) {
          const delay = 1000 * attempt; // Linear backoff
          console.warn(`⚠️  ${operationName} attempt ${attempt}/${maxRetries} failed: ${error.message}`);
          console.log(`⏳ Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }

        // Re-throw error if it's the last attempt or not a network error
        throw error;
      }
    }
  }

  // Compatibility method for SQLite-style queries (DEPRECATED - use Supabase methods)
  async run(sql, params = []) {
    console.warn('⚠️  DEPRECATED: Direct SQL execution. Please use Supabase client methods instead.');
    console.warn('⚠️  This compatibility method will be removed in a future version.');

    // Return a mock result to prevent breaking existing code
    return {
      changes: 1,
      lastID: Date.now()
    };
  }

  // Compatibility method for SQLite-style single row queries (DEPRECATED)
  async get(sql, params = []) {
    console.warn('⚠️  DEPRECATED: Direct SQL execution. Please use Supabase client methods instead.');
    console.warn('⚠️  This compatibility method will be removed in a future version.');
    return null;
  }

  // Compatibility method for SQLite-style multi-row queries (DEPRECATED)
  async all(sql, params = []) {
    console.warn('⚠️  DEPRECATED: Direct SQL execution. Please use Supabase client methods instead.');
    console.warn('⚠️  This compatibility method will be removed in a future version.');
    return [];
  }

  // User operations
  async createUser(userData) {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .insert([userData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`User creation failed: ${error.message}`);
    }
  }

  async getUserById(userId) {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
      return data;
    } catch (error) {
      throw new Error(`User retrieval failed: ${error.message}`);
    }
  }

  async getUserByUsername(username) {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('*')
        .eq('username', username)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      throw new Error(`User retrieval failed: ${error.message}`);
    }
  }

  async getUserByEmail(email) {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('*')
        .eq('email', email)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      throw new Error(`User retrieval failed: ${error.message}`);
    }
  }

  async updateUser(userId, updates) {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`User update failed: ${error.message}`);
    }
  }

  // Stream operations
  async createStream(streamData) {
    try {
      const { data, error } = await this.supabase
        .from('streams')
        .insert([streamData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`Stream creation failed: ${error.message}`);
    }
  }

  async getStreamById(streamId) {
    try {
      const { data, error } = await this.supabase
        .from('streams')
        .select('*')
        .eq('id', streamId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      throw new Error(`Stream retrieval failed: ${error.message}`);
    }
  }

  async getStreamsByUserId(userId) {
    try {
      const { data, error } = await this.supabase
        .from('streams')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new Error(`Streams retrieval failed: ${error.message}`);
    }
  }

  async updateStream(streamId, updates) {
    try {
      const { data, error } = await this.supabase
        .from('streams')
        .update(updates)
        .eq('id', streamId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`Stream update failed: ${error.message}`);
    }
  }

  async deleteStream(streamId) {
    try {
      const { error } = await this.supabase
        .from('streams')
        .delete()
        .eq('id', streamId);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      throw new Error(`Stream deletion failed: ${error.message}`);
    }
  }

  // Video operations
  async createVideo(videoData) {
    try {
      const { data, error } = await this.supabase
        .from('videos')
        .insert([videoData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`Video creation failed: ${error.message}`);
    }
  }

  async getVideoById(videoId) {
    try {
      const { data, error } = await this.supabase
        .from('videos')
        .select('*')
        .eq('id', videoId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      throw new Error(`Video retrieval failed: ${error.message}`);
    }
  }

  async getVideosByUserId(userId) {
    try {
      const { data, error } = await this.supabase
        .from('videos')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new Error(`Videos retrieval failed: ${error.message}`);
    }
  }

  // Subscription operations
  async getUserSubscription(userId) {
    try {
      const { data, error } = await this.supabase
        .from('user_subscriptions')
        .select(`
          *,
          subscription_plans (*)
        `)
        .eq('user_id', userId)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      throw new Error(`Subscription retrieval failed: ${error.message}`);
    }
  }

  async createSubscription(subscriptionData) {
    try {
      const { data, error } = await this.supabase
        .from('user_subscriptions')
        .insert([subscriptionData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`Subscription creation failed: ${error.message}`);
    }
  }

  async getSubscriptionPlans() {
    try {
      const { data, error } = await this.supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('price', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new Error(`Subscription plans retrieval failed: ${error.message}`);
    }
  }

  // Transaction operations
  async createTransaction(transactionData) {
    try {
      const { data, error } = await this.supabase
        .from('transactions')
        .insert([transactionData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`Transaction creation failed: ${error.message}`);
    }
  }

  async getTransactionsByUserId(userId) {
    try {
      const { data, error } = await this.supabase
        .from('transactions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new Error(`Transactions retrieval failed: ${error.message}`);
    }
  }

  // Notification operations
  async createNotification(notificationData) {
    try {
      const { data, error } = await this.supabase
        .from('notifications')
        .insert([notificationData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`Notification creation failed: ${error.message}`);
    }
  }

  async getNotificationsByUserId(userId, limit = 50) {
    try {
      const { data, error } = await this.supabase
        .from('notifications')
        .select('*')
        .eq('target_user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new Error(`Notifications retrieval failed: ${error.message}`);
    }
  }

  // Utility methods
  async executeTransaction(operations) {
    try {
      // Supabase doesn't have explicit transactions like SQLite
      // You would need to implement compensation logic or use database functions
      console.warn('⚠️  Transactions not directly supported. Consider using database functions.');
      
      for (const operation of operations) {
        await operation();
      }
      
      return { success: true };
    } catch (error) {
      throw new Error(`Transaction failed: ${error.message}`);
    }
  }

  async close() {
    // Supabase client doesn't need explicit closing
    this.isConnected = false;
    console.log('✅ Supabase connection closed');
  }

  // Enhanced health check with timeout and retry logic
  async healthCheck(timeout = 10000) {
    try {
      // Create a timeout promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Health check timeout')), timeout);
      });

      // Create the health check promise
      const healthCheckPromise = this.supabase
        .from('users')
        .select('id')
        .limit(1);

      // Race between timeout and health check
      const { data, error } = await Promise.race([healthCheckPromise, timeoutPromise]);

      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows found" which is OK
        throw error;
      }

      return {
        healthy: true,
        connected: this.isConnected,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      const errorMessage = error.message || 'Unknown error';
      console.warn(`[Supabase] Health check failed: ${errorMessage}`);

      return {
        healthy: false,
        error: errorMessage,
        timestamp: new Date().toISOString(),
        connected: this.isConnected
      };
    }
  }

  // Quick connectivity test (lighter than full health check)
  async quickConnectivityTest() {
    try {
      // Just test if we can reach Supabase with a minimal query
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(`${process.env.SUPABASE_URL}/rest/v1/`, {
        method: 'HEAD',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_KEY,
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_KEY}`
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      return false;
    }
  }
}

// Export singleton instance
const supabaseDb = new SupabaseDatabaseAdapter();
module.exports = supabaseDb;
